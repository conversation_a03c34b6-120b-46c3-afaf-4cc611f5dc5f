"""
Enhanced Pothole Video Processing Module

This module implements the specific requirements for pothole detection and tracking in videos:
1. Frame Processing Logic - Process every 5th frame
2. Pothole Detection and Mask Preparation - YOLO with confidence 0.2
3. Area and Depth Calculation Logic - Using calculate_pothole_dimensions and calculate_real_depth
4. Pothole Tracking with Unique ID - Global tracker with IoU > 0.1
5. Volume Storage and Maximum Volume Per Pothole - Track max volume per pothole
6. GPS Coordinate Mapping - Map GPS coordinates to frames
7. UI Logic - Convert to DataFrame and create volume bins
"""

import cv2
import numpy as np
import pandas as pd
import time
import torch
from datetime import datetime
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

class EnhancedPotholeTracker:
    """
    Enhanced Pothole Tracker implementing the specific requirements:
    - Global pothole_tracker = {} and next_pothole_id = 0
    - IoU > 0.1 for matching
    - Frame expiry of 5 seconds
    - Maximum volume tracking per pothole
    """
    
    def __init__(self, iou_threshold=0.1, frame_expiry_seconds=5, fps=30):
        # Global tracking variables as specified
        self.pothole_tracker = {}  # Global pothole tracker
        self.next_pothole_id = 0   # Global next pothole ID
        
        # Configuration
        self.iou_threshold = iou_threshold  # IoU > 0.1 as specified
        self.frame_expiry = frame_expiry_seconds * fps  # 5 seconds frame expiry
        self.fps = fps
        
        # Volume storage for maximum volume per pothole
        self.pothole_max_volume = {}  # {pothole_id: (area, depth, volume, time, gps)}
        
        # GPS coordinate mapping
        self.df_cleaned = None  # Will be set externally for GPS mapping
        
    def set_gps_dataframe(self, df_cleaned):
        """Set the cleaned GPS DataFrame for coordinate mapping"""
        self.df_cleaned = df_cleaned
    
    def calculate_iou(self, box1, box2):
        """Calculate Intersection over Union (IoU) between two bounding boxes"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2

        # Calculate intersection area
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)

        # Calculate union area
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def get_gps_coordinates(self, frame_timestamp):
        """
        Retrieve GPS coordinates from cleaned DataFrame for current frame timestamp
        Default to (None, None) if not found
        """
        if self.df_cleaned is None:
            return (None, None)
        
        try:
            # Find closest timestamp in GPS data
            closest_row = self.df_cleaned.iloc[(self.df_cleaned['timestamp'] - frame_timestamp).abs().argsort()[:1]]
            if not closest_row.empty:
                return (closest_row.iloc[0]['latitude'], closest_row.iloc[0]['longitude'])
        except Exception as e:
            logger.warning(f"Error retrieving GPS coordinates: {e}")
        
        return (None, None)

    def process_frame(self, frame, frame_count, mode, selections, models, midas, midas_transform, depth_map=None):
        """
        Process a single frame according to the requirements:
        1. Only process every 5th frame
        2. Trigger logic only if mode == "pavement" and "Potholes" is selected
        3. Use YOLO with confidence threshold 0.2
        4. Calculate area and depth, then volume
        5. Track potholes with unique IDs
        6. Store maximum volume per pothole
        """
        
        # 1. Frame Processing Logic - Only process every 5th frame
        if frame_count % 5 != 0:
            return frame, []
        
        # 2. Trigger logic only if mode == "pavement" and "Potholes" is selected
        if mode != "pavement" or "Potholes" not in selections.get("Class", []):
            return frame, []
        
        detection_frame = frame.copy()
        pothole_detections = []
        
        try:
            # 3. Pothole Detection and Mask Preparation
            # Use detection model (YOLO) with confidence threshold of 0.2
            if "potholes" not in models:
                logger.warning("Pothole model not available")
                return frame, []
            
            # Convert frame for inference
            if frame.shape[2] == 3:
                inference_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            else:
                inference_frame = frame
            
            # Run YOLO inference with confidence 0.2
            with torch.no_grad():
                results = models["potholes"](inference_frame, conf=0.2)
            
            # Process each detection
            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    
                    # Check if segmentation masks are available
                    if result.masks is not None:
                        masks = result.masks.data.cpu().numpy()
                        
                        for mask, box, conf in zip(masks, boxes, confidences):
                            # Extract segmentation mask and bounding box
                            binary_mask = (mask > 0.5).astype(np.uint8) * 255  # Threshold at 0.5
                            binary_mask = cv2.resize(binary_mask, (frame.shape[1], frame.shape[0]))  # Resize to frame shape
                            
                            # 4. Area and Depth Calculation Logic
                            # Use calculate_pothole_dimensions to compute area_cm2
                            from utils.models import calculate_pothole_dimensions, calculate_real_depth
                            
                            dimensions = calculate_pothole_dimensions(binary_mask)
                            if not dimensions:
                                continue
                            
                            area_cm2 = dimensions["area_cm2"]
                            
                            # Use calculate_real_depth to compute max_depth_cm
                            if depth_map is not None:
                                depth_metrics = calculate_real_depth(binary_mask, depth_map)
                                max_depth_cm = depth_metrics["max_depth_cm"] if depth_metrics else 5.0
                            else:
                                max_depth_cm = 5.0  # Default depth
                            
                            # Calculate volume = area_cm2 * max_depth_cm
                            volume = area_cm2 * max_depth_cm
                            
                            # Get GPS coordinates for current frame
                            frame_timestamp = frame_count / self.fps
                            gps_coords = self.get_gps_coordinates(frame_timestamp)
                            
                            # 5. Pothole Tracking with Unique ID
                            bbox = [int(x) for x in box[:4]]
                            current_time = frame_count
                            
                            # Remove expired potholes (not seen in last 5 seconds)
                            expired_ids = []
                            for pothole_id, track_data in self.pothole_tracker.items():
                                if current_time - track_data['last_seen_time'] > self.frame_expiry:
                                    expired_ids.append(pothole_id)
                            
                            for expired_id in expired_ids:
                                del self.pothole_tracker[expired_id]
                            
                            # Match with existing potholes using IoU > 0.1
                            matched_id = None
                            best_iou = 0.0
                            
                            for pothole_id, track_data in self.pothole_tracker.items():
                                iou = self.calculate_iou(bbox, track_data['bounding_box'])
                                if iou > self.iou_threshold and iou > best_iou:
                                    best_iou = iou
                                    matched_id = pothole_id
                            
                            if matched_id is not None:
                                # Update existing pothole
                                self.pothole_tracker[matched_id].update({
                                    'bounding_box': bbox,
                                    'last_seen_time': current_time,
                                    'gps_coords': gps_coords
                                })
                                current_pothole_id = matched_id
                            else:
                                # Assign new pothole_id and increment next_pothole_id
                                current_pothole_id = self.next_pothole_id
                                self.next_pothole_id += 1
                                
                                self.pothole_tracker[current_pothole_id] = {
                                    'bounding_box': bbox,
                                    'last_seen_time': current_time,
                                    'gps_coords': gps_coords
                                }
                            
                            # 6. Volume Storage and Maximum Volume Per Pothole
                            # Only update if new volume is larger than existing one
                            if (current_pothole_id not in self.pothole_max_volume or 
                                volume > self.pothole_max_volume[current_pothole_id][2]):
                                
                                self.pothole_max_volume[current_pothole_id] = (
                                    area_cm2,      # area
                                    max_depth_cm,  # depth
                                    volume,        # volume
                                    frame_timestamp,  # time
                                    gps_coords     # gps
                                )
                            
                            # Draw visualization
                            x1, y1, x2, y2 = bbox
                            
                            # Apply colored overlay for mask
                            mask_indices = binary_mask > 0
                            detection_frame[mask_indices] = cv2.addWeighted(
                                detection_frame[mask_indices], 0.7,
                                np.full_like(detection_frame[mask_indices], (255, 0, 0)), 0.3, 0
                            )
                            
                            # Calculate volume range
                            if volume < 1000:
                                volume_range = "Small (<1k)"
                            elif volume < 10000:
                                volume_range = "Medium (1k-10k)"
                            else:
                                volume_range = "Big (>10k)"

                            # Draw bounding box and label
                            cv2.rectangle(detection_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                            label = f"ID {current_pothole_id}, A:{area_cm2:.1f}cm², D:{max_depth_cm:.1f}cm, V:{volume:.1f}cm³"
                            cv2.putText(detection_frame, label, (x1, y1 - 10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                            # Add to detections list
                            pothole_detections.append({
                                'type': 'Pothole',
                                'pothole_id': current_pothole_id,
                                'bbox': bbox,
                                'area_cm2': area_cm2,
                                'depth_cm': max_depth_cm,
                                'volume_cm3': volume,
                                'volume_range': volume_range,
                                'confidence': float(conf),
                                'gps_coords': gps_coords,
                                'timestamp': frame_timestamp,
                                'frame_number': frame_count
                            })
            
            return detection_frame, pothole_detections
            
        except Exception as e:
            logger.error(f"Error processing frame {frame_count}: {str(e)}")
            return frame, []
    
    def get_ui_data(self):
        """
        7. UI Logic - Convert pothole_max_volume to DataFrame and create volume bins
        """
        if not self.pothole_max_volume:
            return pd.DataFrame(), {}
        
        # Convert pothole_max_volume to DataFrame
        df_data = []
        for pothole_id, (area, depth, volume, time, gps) in self.pothole_max_volume.items():
            df_data.append({
                'Pothole ID': pothole_id,
                'Time': time,
                'GPS': f"{gps[0]}, {gps[1]}" if gps[0] is not None else "Not Available",
                'Area': area,
                'Depth': depth,
                'Volume': volume
            })
        
        df_potholes = pd.DataFrame(df_data)
        
        # Use pd.cut to bin Volume into categories
        volume_bins = [0, 1000, 10000, float('inf')]
        volume_labels = ["Small (<1k)", "Medium (1k-10k)", "Big (>10k)"]
        
        df_potholes['Volume_Category'] = pd.cut(
            df_potholes['Volume'], 
            bins=volume_bins, 
            labels=volume_labels, 
            right=False
        )
        
        # Count each bin category
        volume_counts = df_potholes['Volume_Category'].value_counts().to_dict()
        
        # Ensure all categories are present
        for label in volume_labels:
            if label not in volume_counts:
                volume_counts[label] = 0
        
        return df_potholes, volume_counts
    
    def get_tracking_summary(self):
        """Get summary of current tracking state"""
        return {
            'total_potholes_tracked': len(self.pothole_max_volume),
            'active_tracks': len(self.pothole_tracker),
            'next_id': self.next_pothole_id,
            'max_volumes': dict(self.pothole_max_volume)
        }


def process_pothole_video_enhanced(video_path, mode, selections, models, midas, midas_transform, df_cleaned=None):
    """
    Enhanced video processing function implementing all requirements
    
    Args:
        video_path: Path to video file
        mode: Processing mode (should be "pavement")
        selections: Dictionary with selected classes (should contain "Potholes")
        models: Dictionary of loaded models
        midas: MiDaS depth estimation model
        midas_transform: MiDaS transform
        df_cleaned: GPS DataFrame for coordinate mapping
    
    Returns:
        Generator yielding processing results
    """
    
    # Initialize enhanced pothole tracker
    tracker = EnhancedPotholeTracker(iou_threshold=0.1, frame_expiry_seconds=5, fps=30)
    
    # Set GPS DataFrame if provided
    if df_cleaned is not None:
        tracker.set_gps_dataframe(df_cleaned)
    
    try:
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            yield {"success": False, "message": "Could not open video file"}
            return
        
        # Get video properties
        frame_rate = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"Processing video: {total_frames} frames at {frame_rate} FPS")
        
        frame_count = 0
        all_detections = []
        
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Generate depth map for current frame (if processing frame)
            depth_map = None
            if frame_count % 5 == 0 and midas is not None and midas_transform is not None:
                try:
                    from utils.models import estimate_depth
                    depth_map = estimate_depth(frame, midas, midas_transform)
                except Exception as e:
                    logger.warning(f"Could not generate depth map for frame {frame_count}: {e}")
            
            # Process frame with enhanced tracker
            processed_frame, detections = tracker.process_frame(
                frame, frame_count, mode, selections, models, midas, midas_transform, depth_map
            )
            
            # Add detections to overall list
            all_detections.extend(detections)
            
            # Yield progress update every 30 frames
            if frame_count % 30 == 0:
                progress = (frame_count / total_frames) * 100
                ui_data, volume_counts = tracker.get_ui_data()
                
                yield {
                    "frame_count": frame_count,
                    "total_frames": total_frames,
                    "progress": progress,
                    "current_detections": detections,
                    "tracking_summary": tracker.get_tracking_summary(),
                    "ui_dataframe": ui_data.to_dict('records') if not ui_data.empty else [],
                    "volume_distribution": volume_counts
                }
        
        # Final results
        cap.release()
        
        # Get final UI data
        df_potholes, volume_counts = tracker.get_ui_data()
        
        yield {
            "success": True,
            "completed": True,
            "total_frames": frame_count,
            "total_detections": len(all_detections),
            "all_detections": all_detections,
            "tracking_summary": tracker.get_tracking_summary(),
            "final_dataframe": df_potholes.to_dict('records') if not df_potholes.empty else [],
            "volume_distribution": volume_counts,
            "volume_chart_data": {
                "categories": list(volume_counts.keys()),
                "values": list(volume_counts.values()),
                "colors": ["#28a745", "#ffc107", "#dc3545"]  # Green, Yellow, Red
            }
        }
        
    except Exception as e:
        logger.error(f"Error during enhanced video processing: {str(e)}")
        yield {"success": False, "message": str(e)}
    finally:
        if 'cap' in locals():
            cap.release()


# Example usage and testing
if __name__ == "__main__":
    # This would be used in the main application
    print("Enhanced Pothole Video Processor - Ready for integration")
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTA_GIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\VideoDefectDetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Card, Button, Form, Alert, Spinner, Table, Row, Col } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport useResponsive from '../hooks/useResponsive';\nimport './VideoDefectDetection.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoDefectDetection = () => {\n  _s();\n  const [selectedModel, setSelectedModel] = useState('All');\n  const [videoFile, setVideoFile] = useState(null);\n  const [videoPreview, setVideoPreview] = useState(null);\n  const [processedVideo, setProcessedVideo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [shouldStop, setShouldStop] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [inputSource, setInputSource] = useState('video');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [recordedChunks, setRecordedChunks] = useState([]);\n\n  // Video processing states\n  const [frameBuffer, setFrameBuffer] = useState([]);\n  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);\n  const [isBuffering, setIsBuffering] = useState(false);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [processingProgress, setProcessingProgress] = useState(0);\n  const [allDetections, setAllDetections] = useState([]);\n  const [videoResults, setVideoResults] = useState(null);\n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const recordingTimerRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const {\n    isMobile\n  } = useResponsive();\n  const BUFFER_SIZE = 10;\n  const PLAYBACK_FPS = 15;\n  const MAX_RECORDING_TIME = 60; // 1 minute limit\n\n  // Available models\n  const modelOptions = [{\n    value: 'All',\n    label: 'All (detect all types of defects)'\n  }, {\n    value: 'Potholes',\n    label: 'Potholes'\n  }, {\n    value: 'Alligator Cracks',\n    label: 'Alligator Cracks'\n  }, {\n    value: 'Kerbs',\n    label: 'Kerbs'\n  }];\n\n  // Get user location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        const {\n          latitude,\n          longitude\n        } = position.coords;\n        setCoordinates(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);\n      }, err => {\n        console.error(\"Error getting location:\", err);\n        setCoordinates('Location unavailable');\n      });\n    }\n  }, []);\n\n  // Recording timer\n  useEffect(() => {\n    if (isRecording) {\n      recordingTimerRef.current = setInterval(() => {\n        setRecordingTime(prev => {\n          if (prev >= MAX_RECORDING_TIME) {\n            handleStopRecording();\n            return MAX_RECORDING_TIME;\n          }\n          return prev + 1;\n        });\n      }, 1000);\n    } else {\n      if (recordingTimerRef.current) {\n        clearInterval(recordingTimerRef.current);\n      }\n    }\n    return () => {\n      if (recordingTimerRef.current) {\n        clearInterval(recordingTimerRef.current);\n      }\n    };\n  }, [isRecording]);\n\n  // Video playback effect\n  useEffect(() => {\n    let playbackInterval;\n    if (isPlaying && frameBuffer.length > 0) {\n      playbackInterval = setInterval(() => {\n        setCurrentFrameIndex(prev => {\n          if (prev < frameBuffer.length - 1) {\n            return prev + 1;\n          } else {\n            setIsPlaying(false);\n            return prev;\n          }\n        });\n      }, 1000 / PLAYBACK_FPS);\n    }\n    return () => {\n      if (playbackInterval) clearInterval(playbackInterval);\n    };\n  }, [isPlaying, frameBuffer]);\n\n  // Update processed video when frame changes\n  useEffect(() => {\n    if (frameBuffer.length > 0 && currentFrameIndex < frameBuffer.length) {\n      setProcessedVideo(frameBuffer[currentFrameIndex]);\n    }\n  }, [currentFrameIndex, frameBuffer]);\n\n  // Handle video file selection\n  const handleVideoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setVideoFile(file);\n      setVideoPreview(URL.createObjectURL(file));\n      setProcessedVideo(null);\n      setVideoResults(null);\n      setAllDetections([]);\n      setError('');\n    }\n  };\n\n  // Handle camera activation\n  const toggleCamera = () => {\n    setCameraActive(!cameraActive);\n    if (!cameraActive) {\n      setVideoFile(null);\n      setVideoPreview(null);\n      setProcessedVideo(null);\n      setVideoResults(null);\n      setAllDetections([]);\n      setError('');\n    }\n  };\n\n  // Start recording\n  const handleStartRecording = async () => {\n    if (!webcamRef.current || !webcamRef.current.stream) {\n      setError('Camera not available');\n      return;\n    }\n    try {\n      setRecordedChunks([]);\n      setRecordingTime(0);\n      setIsRecording(true);\n      setError('');\n      const mediaRecorder = new MediaRecorder(webcamRef.current.stream, {\n        mimeType: 'video/webm'\n      });\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          setRecordedChunks(prev => [...prev, event.data]);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(recordedChunks, {\n          type: 'video/webm'\n        });\n        const file = new File([blob], `recorded_video_${Date.now()}.webm`, {\n          type: 'video/webm'\n        });\n        setVideoFile(file);\n        setVideoPreview(URL.createObjectURL(blob));\n        setIsRecording(false);\n        setRecordingTime(0);\n      };\n      mediaRecorder.start();\n    } catch (error) {\n      setError('Failed to start recording: ' + error.message);\n      setIsRecording(false);\n    }\n  };\n\n  // Stop recording\n  const handleStopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setRecordingTime(0);\n    }\n  };\n\n  // Toggle camera orientation\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Check if ready for processing\n  const isReadyForProcessing = () => {\n    return inputSource === 'video' && videoFile || inputSource === 'camera' && videoFile;\n  };\n\n  // Handle video processing\n  const handleProcess = async () => {\n    if (!isReadyForProcessing()) {\n      setError('Please provide a video file first');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setIsProcessing(true);\n    setShouldStop(false);\n    setIsBuffering(true);\n    setIsPlaying(false);\n    setFrameBuffer([]);\n    setCurrentFrameIndex(0);\n    setProcessingProgress(0);\n    setAllDetections([]);\n    setProcessedVideo(null); // Reset processed video\n    setVideoResults(null); // Reset video results\n\n    try {\n      const formData = new FormData();\n      formData.append('video', videoFile);\n      formData.append('selectedModel', selectedModel);\n      formData.append('coordinates', coordinates);\n      console.log('Starting video processing with model:', selectedModel);\n\n      // Create FormData for SSE request\n      const sseUrl = '/api/pavement/detect-video';\n\n      // Use fetch for SSE with FormData\n      const response = await fetch(sseUrl, {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      // Handle SSE stream\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n      const processStream = async () => {\n        try {\n          while (true) {\n            const {\n              done,\n              value\n            } = await reader.read();\n            if (done) {\n              console.log('Stream ended naturally');\n              setIsProcessing(false);\n              setLoading(false);\n              setIsBuffering(false);\n              break;\n            }\n            const chunk = decoder.decode(value, {\n              stream: true\n            });\n            const lines = chunk.split('\\n');\n            for (const line of lines) {\n              if (line.startsWith('data: ')) {\n                try {\n                  const data = JSON.parse(line.substring(6));\n                  console.log('Received SSE data:', {\n                    hasFrame: !!data.frame,\n                    frameLength: data.frame ? data.frame.length : 0,\n                    progress: data.progress,\n                    frameCount: data.frame_count,\n                    detections: data.detections ? data.detections.length : 0\n                  });\n                  if (data.success === false) {\n                    setError(data.message || 'Video processing failed');\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    return;\n                  }\n                  if (data.frame && typeof data.frame === 'string' && data.frame.length > 1000) {\n                    // Update frame buffer and current index for real-time display\n                    setFrameBuffer(prev => {\n                      const newBuffer = [...prev, data.frame];\n\n                      // Update current frame index to the latest frame for live preview\n                      setCurrentFrameIndex(newBuffer.length - 1);\n\n                      // Start showing frames immediately\n                      if (newBuffer.length === 1) {\n                        setIsBuffering(false);\n                        setIsPlaying(false); // Don't auto-play during live processing\n                        setProcessedVideo(data.frame); // Show the first frame immediately\n                      }\n                      return newBuffer;\n                    });\n\n                    // Update the displayed frame for real-time preview\n                    setProcessedVideo(data.frame);\n                  }\n\n                  // Update progress\n                  if (data.progress !== undefined) {\n                    setProcessingProgress(data.progress);\n                    console.log(`Processing progress: ${data.progress.toFixed(1)}%`);\n                  }\n\n                  // Update detections\n                  if (data.detections && data.detections.length > 0) {\n                    setAllDetections(prev => [...prev, ...data.detections]);\n                  }\n\n                  // Handle final results\n                  if (data.all_detections) {\n                    setVideoResults(data);\n                    setAllDetections(data.all_detections);\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    setProcessingProgress(100); // Ensure progress shows 100%\n\n                    // Reset to first frame for playback after processing\n                    setCurrentFrameIndex(0);\n                    setIsPlaying(false);\n                    console.log('Video processing completed');\n                    console.log(`Total unique detections: ${data.total_unique_detections || data.all_detections.length}`);\n                    console.log(`Total frame detections: ${data.total_frame_detections || data.all_detections.length}`);\n                    console.log(`Total frames processed: ${frameBuffer.length}`);\n                    return;\n                  }\n\n                  // Handle explicit end signal\n                  if (data.end) {\n                    console.log('Received end signal, closing stream');\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    return;\n                  }\n                } catch (parseError) {\n                  console.warn('Error parsing SSE data:', parseError);\n                }\n              }\n            }\n          }\n        } catch (streamError) {\n          console.error('Stream processing error:', streamError);\n          setError('Error processing video stream');\n          setIsProcessing(false);\n          setLoading(false);\n          setIsBuffering(false);\n        } finally {\n          // Clean up reader\n          if (reader) {\n            try {\n              reader.releaseLock();\n            } catch (e) {\n              console.warn('Error releasing reader lock:', e);\n            }\n          }\n        }\n      };\n      processStream();\n    } catch (error) {\n      console.error('Video processing error:', error);\n      setError(error.message || 'Video processing failed');\n      setLoading(false);\n      setIsProcessing(false);\n    }\n  };\n\n  // Stop processing\n  const handleStopProcessing = async () => {\n    try {\n      await axios.post('/api/pavement/stop-video-processing');\n      setIsProcessing(false);\n      setShouldStop(true);\n      setIsBuffering(false);\n      setIsPlaying(false);\n      setLoading(false);\n      setError('Video processing stopped');\n    } catch (error) {\n      console.error('Error stopping processing:', error);\n      setError('Failed to stop processing');\n    }\n  };\n\n  // Reset all\n  const handleReset = () => {\n    setVideoFile(null);\n    setVideoPreview(null);\n    setProcessedVideo(null);\n    setVideoResults(null);\n    setAllDetections([]);\n    setFrameBuffer([]);\n    setCurrentFrameIndex(0);\n    setIsProcessing(false);\n    setShouldStop(false);\n    setIsBuffering(false);\n    setIsPlaying(false);\n    setProcessingProgress(0);\n    setError('');\n    setSelectedModel('All');\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Playback controls\n  const handlePlayPause = () => setIsPlaying(!isPlaying);\n  const handleRewind = () => setCurrentFrameIndex(Math.max(currentFrameIndex - 5, 0));\n  const handleForward = () => setCurrentFrameIndex(Math.min(currentFrameIndex + 5, frameBuffer.length - 1));\n\n  // Group detections by type\n  const getDetectionSummary = () => {\n    const summary = {};\n    allDetections.forEach(det => {\n      summary[det.type] = (summary[det.type] || 0) + 1;\n    });\n    return summary;\n  };\n\n  // Get tracking statistics\n  const getTrackingStats = () => {\n    // Helper function to get unique detections count\n    const getUniqueCount = (detections, idField = 'pothole_id') => {\n      const uniqueIds = new Set();\n      detections.forEach(detection => {\n        const id = detection[idField] || detection.track_id;\n        if (id !== undefined) {\n          uniqueIds.add(id);\n        }\n      });\n      return uniqueIds.size;\n    };\n    const totalFrameDetections = allDetections.length;\n    const uniquePotholes = getUniqueCount(allDetections.filter(d => d.type === 'Pothole'), 'pothole_id');\n    const uniqueCracks = getUniqueCount(allDetections.filter(d => d.type.includes('Crack')), 'track_id');\n    const uniqueKerbs = getUniqueCount(allDetections.filter(d => d.type.includes('Kerb')), 'track_id');\n    const totalUniqueDetections = uniquePotholes + uniqueCracks + uniqueKerbs;\n    return {\n      uniqueDetections: totalUniqueDetections,\n      frameDetections: totalFrameDetections,\n      duplicatesRemoved: totalFrameDetections - totalUniqueDetections\n    };\n  };\n\n  // Format time\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-defect-detection\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Video Defect Detection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"mb-3\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Detection Model\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: selectedModel,\n                onChange: e => setSelectedModel(e.target.value),\n                disabled: isProcessing,\n                children: modelOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Input Source\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: inputSource,\n                onChange: e => setInputSource(e.target.value),\n                disabled: isProcessing,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"video\",\n                  children: \"Video Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"camera\",\n                  children: \"Live Camera Recording\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), inputSource === 'video' && /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Upload Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"file\",\n                accept: \"video/*\",\n                onChange: handleVideoChange,\n                ref: fileInputRef,\n                disabled: isProcessing\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this), videoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"video\", {\n                  src: videoPreview,\n                  controls: true,\n                  className: \"video-preview\",\n                  style: {\n                    maxHeight: '200px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this), inputSource === 'camera' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: cameraActive ? \"danger\" : \"info\",\n                  onClick: toggleCamera,\n                  disabled: isProcessing,\n                  children: cameraActive ? 'Stop Camera' : 'Start Camera'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this), isMobile && cameraActive && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-secondary\",\n                  onClick: toggleCameraOrientation,\n                  size: \"sm\",\n                  children: \"Rotate Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"webcam-container\",\n                children: [/*#__PURE__*/_jsxDEV(Webcam, {\n                  audio: false,\n                  ref: webcamRef,\n                  screenshotFormat: \"image/jpeg\",\n                  width: \"100%\",\n                  height: \"auto\",\n                  videoConstraints: {\n                    width: 640,\n                    height: 480,\n                    facingMode: cameraOrientation\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2\",\n                  children: !isRecording ? /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"success\",\n                    onClick: handleStartRecording,\n                    disabled: isProcessing,\n                    children: \"Start Recording\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"danger\",\n                      onClick: handleStopRecording,\n                      children: \"Stop Recording\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-danger\",\n                      children: [\"Recording: \", formatTime(recordingTime), \" / \", formatTime(MAX_RECORDING_TIME)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 21\n              }, this), videoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"video\", {\n                  src: videoPreview,\n                  controls: true,\n                  className: \"video-preview\",\n                  style: {\n                    maxHeight: '200px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: handleProcess,\n                disabled: !isReadyForProcessing() || isProcessing,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    size: \"sm\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 23\n                  }, this), \"Processing...\"]\n                }, void 0, true) : 'Process Video'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this), isProcessing && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"warning\",\n                onClick: handleStopProcessing,\n                children: \"Stop Processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: handleReset,\n                disabled: isProcessing,\n                children: \"Reset\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this), isProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Processing Progress:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [processingProgress.toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress mt-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress-bar\",\n                  role: \"progressbar\",\n                  style: {\n                    width: `${processingProgress}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: [processedVideo && /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-success text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Processed Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"processed-video-container\",\n              children: [isBuffering && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"processing-overlay\",\n                children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                  animation: \"border\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ms-2\",\n                  children: \"Buffering video...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                src: processedVideo.startsWith('data:') ? processedVideo : `data:image/jpeg;base64,${processedVideo}`,\n                alt: \"Processed frame\",\n                style: {\n                  maxWidth: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 19\n              }, this), frameBuffer.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-2 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline-primary\",\n                    onClick: handleRewind,\n                    children: \"\\u23EA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline-primary\",\n                    onClick: handlePlayPause,\n                    children: isPlaying ? '⏸️' : '▶️'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline-primary\",\n                    onClick: handleForward,\n                    children: \"\\u23E9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Form.Range, {\n                  min: 0,\n                  max: frameBuffer.length - 1,\n                  value: currentFrameIndex,\n                  onChange: e => setCurrentFrameIndex(Number(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center small text-muted\",\n                  children: [\"Frame \", currentFrameIndex + 1, \" of \", frameBuffer.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 13\n        }, this), allDetections.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Detection Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detection-summary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Detection Summary:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: Object.entries(getDetectionSummary()).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-secondary me-1\",\n                  children: [type, \": \", count]\n                }, type, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tracking-stats\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Tracking Stats:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 23\n                  }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-success me-1\",\n                    children: [\"Unique: \", getTrackingStats().uniqueDetections]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-info me-1\",\n                    children: [\"Total Frames: \", getTrackingStats().frameDetections]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 747,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-warning\",\n                    children: [\"Duplicates Removed: \", getTrackingStats().duplicatesRemoved]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this), (() => {\n              // Helper function to get unique detections by ID with maximum volume\n              const getUniqueDetections = (detections, idField = 'pothole_id') => {\n                const uniqueMap = new Map();\n                detections.forEach(detection => {\n                  const id = detection[idField] || detection.track_id;\n                  if (id !== undefined) {\n                    const existing = uniqueMap.get(id);\n                    if (!existing || (detection.volume_cm3 || detection.volume || 0) > (existing.volume_cm3 || existing.volume || 0)) {\n                      uniqueMap.set(id, detection);\n                    }\n                  }\n                });\n                return Array.from(uniqueMap.values());\n              };\n              const allPotholeDetections = allDetections.filter(d => d.type === 'Pothole');\n              const allCrackDetections = allDetections.filter(d => d.type.includes('Crack'));\n              const allKerbDetections = allDetections.filter(d => d.type.includes('Kerb'));\n\n              // Get unique detections with maximum volumes\n              const potholeDetections = getUniqueDetections(allPotholeDetections, 'pothole_id');\n              const crackDetections = getUniqueDetections(allCrackDetections, 'track_id');\n              const kerbDetections = getUniqueDetections(allKerbDetections, 'track_id');\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [(selectedModel === 'All' || selectedModel === 'Potholes') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section potholes mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-danger\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83D\\uDD73\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 791,\n                      columnNumber: 29\n                    }, this), \"Potholes Detected: \", potholeDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 790,\n                    columnNumber: 27\n                  }, this), potholeDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 799,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area (cm\\xB2)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 800,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Depth (cm)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 801,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Volume (cm\\xB3)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 802,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Volume Range\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 803,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 798,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 797,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: potholeDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.pothole_id || detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 809,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 810,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.max_depth_cm ? detection.max_depth_cm.toFixed(2) : detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 811,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.volume_cm3 || detection.volume ? (detection.volume_cm3 || detection.volume).toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 812,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.volume_range || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 813,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 808,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 806,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 796,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 795,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No potholes detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 25\n                }, this), (selectedModel === 'All' || selectedModel === 'Alligator Cracks') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section cracks mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-success\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83E\\uDEA8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 829,\n                      columnNumber: 29\n                    }, this), \"Cracks Detected: \", crackDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 27\n                  }, this), crackDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 837,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Type\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 838,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area (cm\\xB2)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 839,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area Range\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 840,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 836,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: crackDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 846,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.type\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 847,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 848,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_range || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 849,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 845,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 843,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No cracks detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 25\n                }, this), (selectedModel === 'All' || selectedModel === 'Kerbs') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section kerbs mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-primary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83D\\uDEA7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 29\n                    }, this), \"Kerbs Detected: \", kerbDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 27\n                  }, this), kerbDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 873,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Type\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 874,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Condition\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 875,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Length\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 876,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 872,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 871,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: kerbDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 882,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.kerb_type || 'Concrete Kerb'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 883,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.condition || detection.type\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 884,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.length_m ? detection.length_m.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 885,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 881,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 879,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 870,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No kerbs detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 21\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 470,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoDefectDetection, \"4BTUBSF7Dp0PLMjIRx/ul5V3xgg=\", false, function () {\n  return [useResponsive];\n});\n_c = VideoDefectDetection;\nexport default VideoDefectDetection;\nvar _c;\n$RefreshReg$(_c, \"VideoDefectDetection\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "Spinner", "Table", "Row", "Col", "axios", "Webcam", "useResponsive", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoDefectDetection", "_s", "selected<PERSON><PERSON>l", "setSelectedModel", "videoFile", "setVideoFile", "videoPreview", "setVideoPreview", "processedVideo", "setProcessedVideo", "loading", "setLoading", "error", "setError", "isProcessing", "setIsProcessing", "shouldStop", "setShouldStop", "coordinates", "setCoordinates", "inputSource", "setInputSource", "cameraActive", "setCameraActive", "cameraOrientation", "setCameraOrientation", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "recordedChunks", "setRecordedChunks", "frameBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentFrameIndex", "setCurrentFrameIndex", "isBuffering", "setIsBuffering", "isPlaying", "setIsPlaying", "processingProgress", "setProcessingProgress", "allDetections", "setAllDetections", "videoResults", "setVideoResults", "webcamRef", "fileInputRef", "recordingTimerRef", "mediaRecorderRef", "isMobile", "BUFFER_SIZE", "PLAYBACK_FPS", "MAX_RECORDING_TIME", "modelOptions", "value", "label", "navigator", "geolocation", "getCurrentPosition", "position", "latitude", "longitude", "coords", "toFixed", "err", "console", "current", "setInterval", "prev", "handleStopRecording", "clearInterval", "playbackInterval", "length", "handleVideoChange", "e", "file", "target", "files", "URL", "createObjectURL", "toggleCamera", "handleStartRecording", "stream", "mediaRecorder", "MediaRecorder", "mimeType", "ondataavailable", "event", "data", "size", "onstop", "blob", "Blob", "type", "File", "Date", "now", "start", "message", "stop", "toggleCameraOrientation", "isReadyForProcessing", "handleProcess", "formData", "FormData", "append", "log", "sseUrl", "response", "fetch", "method", "body", "ok", "Error", "status", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "processStream", "done", "read", "chunk", "decode", "lines", "split", "line", "startsWith", "JSON", "parse", "substring", "<PERSON><PERSON><PERSON><PERSON>", "frame", "frameLength", "progress", "frameCount", "frame_count", "detections", "success", "new<PERSON>uffer", "undefined", "all_detections", "total_unique_detections", "total_frame_detections", "end", "parseError", "warn", "streamError", "releaseLock", "handleStopProcessing", "post", "handleReset", "handlePlayPause", "handleRewind", "Math", "max", "handleForward", "min", "getDetectionSummary", "summary", "for<PERSON>ach", "det", "getTrackingStats", "getUniqueCount", "idField", "uniqueIds", "Set", "detection", "id", "track_id", "add", "totalFrameDetections", "uniquePotholes", "filter", "d", "uniqueCracks", "includes", "uniqueKerbs", "totalUniqueDetections", "uniqueDetections", "frameDetections", "duplicates<PERSON><PERSON>oved", "formatTime", "seconds", "mins", "floor", "secs", "toString", "padStart", "className", "children", "md", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "variant", "Group", "Label", "Select", "onChange", "disabled", "map", "option", "Control", "accept", "ref", "src", "controls", "style", "maxHeight", "onClick", "audio", "screenshotFormat", "width", "height", "videoConstraints", "facingMode", "role", "animation", "alt", "max<PERSON><PERSON><PERSON>", "Range", "Number", "Object", "entries", "count", "getUniqueDetections", "uniqueMap", "Map", "existing", "get", "volume_cm3", "volume", "set", "Array", "from", "values", "allPotholeDetections", "allCrackDetections", "allKerbDetections", "potholeDetections", "crackDetections", "kerbDetections", "striped", "bordered", "hover", "index", "pothole_id", "area_cm2", "max_depth_cm", "depth_cm", "volume_range", "area_range", "kerb_type", "condition", "length_m", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTA_GIT/LTA/frontend/src/components/VideoDefectDetection.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Card, Button, Form, Alert, Spinner, Table, Row, Col } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport Webcam from 'react-webcam';\r\nimport useResponsive from '../hooks/useResponsive';\r\nimport './VideoDefectDetection.css';\r\n\r\nconst VideoDefectDetection = () => {\r\n  const [selectedModel, setSelectedModel] = useState('All');\r\n  const [videoFile, setVideoFile] = useState(null);\r\n  const [videoPreview, setVideoPreview] = useState(null);\r\n  const [processedVideo, setProcessedVideo] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [shouldStop, setShouldStop] = useState(false);\r\n  const [coordinates, setCoordinates] = useState('Not Available');\r\n  const [inputSource, setInputSource] = useState('video');\r\n  const [cameraActive, setCameraActive] = useState(false);\r\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [recordedChunks, setRecordedChunks] = useState([]);\r\n  \r\n  // Video processing states\r\n  const [frameBuffer, setFrameBuffer] = useState([]);\r\n  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);\r\n  const [isBuffering, setIsBuffering] = useState(false);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [processingProgress, setProcessingProgress] = useState(0);\r\n  const [allDetections, setAllDetections] = useState([]);\r\n  const [videoResults, setVideoResults] = useState(null);\r\n  \r\n  const webcamRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const recordingTimerRef = useRef(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const { isMobile } = useResponsive();\r\n  \r\n  const BUFFER_SIZE = 10;\r\n  const PLAYBACK_FPS = 15;\r\n  const MAX_RECORDING_TIME = 60; // 1 minute limit\r\n\r\n  // Available models\r\n  const modelOptions = [\r\n    { value: 'All', label: 'All (detect all types of defects)' },\r\n    { value: 'Potholes', label: 'Potholes' },\r\n    { value: 'Alligator Cracks', label: 'Alligator Cracks' },\r\n    { value: 'Kerbs', label: 'Kerbs' }\r\n  ];\r\n\r\n  // Get user location\r\n  useEffect(() => {\r\n    if (navigator.geolocation) {\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          const { latitude, longitude } = position.coords;\r\n          setCoordinates(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);\r\n        },\r\n        (err) => {\r\n          console.error(\"Error getting location:\", err);\r\n          setCoordinates('Location unavailable');\r\n        }\r\n      );\r\n    }\r\n  }, []);\r\n\r\n  // Recording timer\r\n  useEffect(() => {\r\n    if (isRecording) {\r\n      recordingTimerRef.current = setInterval(() => {\r\n        setRecordingTime(prev => {\r\n          if (prev >= MAX_RECORDING_TIME) {\r\n            handleStopRecording();\r\n            return MAX_RECORDING_TIME;\r\n          }\r\n          return prev + 1;\r\n        });\r\n      }, 1000);\r\n    } else {\r\n      if (recordingTimerRef.current) {\r\n        clearInterval(recordingTimerRef.current);\r\n      }\r\n    }\r\n    \r\n    return () => {\r\n      if (recordingTimerRef.current) {\r\n        clearInterval(recordingTimerRef.current);\r\n      }\r\n    };\r\n  }, [isRecording]);\r\n\r\n  // Video playback effect\r\n  useEffect(() => {\r\n    let playbackInterval;\r\n    if (isPlaying && frameBuffer.length > 0) {\r\n      playbackInterval = setInterval(() => {\r\n        setCurrentFrameIndex(prev => {\r\n          if (prev < frameBuffer.length - 1) {\r\n            return prev + 1;\r\n          } else {\r\n            setIsPlaying(false);\r\n            return prev;\r\n          }\r\n        });\r\n      }, 1000 / PLAYBACK_FPS);\r\n    }\r\n    return () => {\r\n      if (playbackInterval) clearInterval(playbackInterval);\r\n    };\r\n  }, [isPlaying, frameBuffer]);\r\n\r\n  // Update processed video when frame changes\r\n  useEffect(() => {\r\n    if (frameBuffer.length > 0 && currentFrameIndex < frameBuffer.length) {\r\n      setProcessedVideo(frameBuffer[currentFrameIndex]);\r\n    }\r\n  }, [currentFrameIndex, frameBuffer]);\r\n\r\n  // Handle video file selection\r\n  const handleVideoChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setVideoFile(file);\r\n      setVideoPreview(URL.createObjectURL(file));\r\n      setProcessedVideo(null);\r\n      setVideoResults(null);\r\n      setAllDetections([]);\r\n      setError('');\r\n    }\r\n  };\r\n\r\n  // Handle camera activation\r\n  const toggleCamera = () => {\r\n    setCameraActive(!cameraActive);\r\n    if (!cameraActive) {\r\n      setVideoFile(null);\r\n      setVideoPreview(null);\r\n      setProcessedVideo(null);\r\n      setVideoResults(null);\r\n      setAllDetections([]);\r\n      setError('');\r\n    }\r\n  };\r\n\r\n  // Start recording\r\n  const handleStartRecording = async () => {\r\n    if (!webcamRef.current || !webcamRef.current.stream) {\r\n      setError('Camera not available');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setRecordedChunks([]);\r\n      setRecordingTime(0);\r\n      setIsRecording(true);\r\n      setError('');\r\n\r\n      const mediaRecorder = new MediaRecorder(webcamRef.current.stream, {\r\n        mimeType: 'video/webm'\r\n      });\r\n\r\n      mediaRecorderRef.current = mediaRecorder;\r\n\r\n      mediaRecorder.ondataavailable = (event) => {\r\n        if (event.data.size > 0) {\r\n          setRecordedChunks(prev => [...prev, event.data]);\r\n        }\r\n      };\r\n\r\n      mediaRecorder.onstop = () => {\r\n        const blob = new Blob(recordedChunks, { type: 'video/webm' });\r\n        const file = new File([blob], `recorded_video_${Date.now()}.webm`, { type: 'video/webm' });\r\n        setVideoFile(file);\r\n        setVideoPreview(URL.createObjectURL(blob));\r\n        setIsRecording(false);\r\n        setRecordingTime(0);\r\n      };\r\n\r\n      mediaRecorder.start();\r\n    } catch (error) {\r\n      setError('Failed to start recording: ' + error.message);\r\n      setIsRecording(false);\r\n    }\r\n  };\r\n\r\n  // Stop recording\r\n  const handleStopRecording = () => {\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      setIsRecording(false);\r\n      setRecordingTime(0);\r\n    }\r\n  };\r\n\r\n  // Toggle camera orientation\r\n  const toggleCameraOrientation = () => {\r\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\r\n  };\r\n\r\n  // Check if ready for processing\r\n  const isReadyForProcessing = () => {\r\n    return (inputSource === 'video' && videoFile) || \r\n           (inputSource === 'camera' && videoFile);\r\n  };\r\n\r\n  // Handle video processing\r\n  const handleProcess = async () => {\r\n    if (!isReadyForProcessing()) {\r\n      setError('Please provide a video file first');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setIsProcessing(true);\r\n    setShouldStop(false);\r\n    setIsBuffering(true);\r\n    setIsPlaying(false);\r\n    setFrameBuffer([]);\r\n    setCurrentFrameIndex(0);\r\n    setProcessingProgress(0);\r\n    setAllDetections([]);\r\n    setProcessedVideo(null); // Reset processed video\r\n    setVideoResults(null);   // Reset video results\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('video', videoFile);\r\n      formData.append('selectedModel', selectedModel);\r\n      formData.append('coordinates', coordinates);\r\n\r\n      console.log('Starting video processing with model:', selectedModel);\r\n\r\n      // Create FormData for SSE request\r\n      const sseUrl = '/api/pavement/detect-video';\r\n      \r\n      // Use fetch for SSE with FormData\r\n      const response = await fetch(sseUrl, {\r\n        method: 'POST',\r\n        body: formData\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      // Handle SSE stream\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder();\r\n\r\n      const processStream = async () => {\r\n        try {\r\n          while (true) {\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              console.log('Stream ended naturally');\r\n              setIsProcessing(false);\r\n              setLoading(false);\r\n              setIsBuffering(false);\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value, { stream: true });\r\n            const lines = chunk.split('\\n');\r\n\r\n            for (const line of lines) {\r\n              if (line.startsWith('data: ')) {\r\n                try {\r\n                  const data = JSON.parse(line.substring(6));\r\n                  console.log('Received SSE data:', {\r\n                    hasFrame: !!data.frame,\r\n                    frameLength: data.frame ? data.frame.length : 0,\r\n                    progress: data.progress,\r\n                    frameCount: data.frame_count,\r\n                    detections: data.detections ? data.detections.length : 0\r\n                  });\r\n\r\n                  if (data.success === false) {\r\n                    setError(data.message || 'Video processing failed');\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    return;\r\n                  }\r\n\r\n                  if (data.frame && typeof data.frame === 'string' && data.frame.length > 1000) {\r\n                    // Update frame buffer and current index for real-time display\r\n                    setFrameBuffer(prev => {\r\n                      const newBuffer = [...prev, data.frame];\r\n                      \r\n                      // Update current frame index to the latest frame for live preview\r\n                      setCurrentFrameIndex(newBuffer.length - 1);\r\n                      \r\n                      // Start showing frames immediately\r\n                      if (newBuffer.length === 1) {\r\n                        setIsBuffering(false);\r\n                        setIsPlaying(false); // Don't auto-play during live processing\r\n                        setProcessedVideo(data.frame); // Show the first frame immediately\r\n                      }\r\n                      \r\n                      return newBuffer;\r\n                    });\r\n                    \r\n                    // Update the displayed frame for real-time preview\r\n                    setProcessedVideo(data.frame);\r\n                  }\r\n\r\n                  // Update progress\r\n                  if (data.progress !== undefined) {\r\n                    setProcessingProgress(data.progress);\r\n                    console.log(`Processing progress: ${data.progress.toFixed(1)}%`);\r\n                  }\r\n\r\n                  // Update detections\r\n                  if (data.detections && data.detections.length > 0) {\r\n                    setAllDetections(prev => [...prev, ...data.detections]);\r\n                  }\r\n\r\n                  // Handle final results\r\n                  if (data.all_detections) {\r\n                    setVideoResults(data);\r\n                    setAllDetections(data.all_detections);\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    setProcessingProgress(100); // Ensure progress shows 100%\r\n                    \r\n                    // Reset to first frame for playback after processing\r\n                    setCurrentFrameIndex(0);\r\n                    setIsPlaying(false);\r\n                    \r\n                    console.log('Video processing completed');\r\n                    console.log(`Total unique detections: ${data.total_unique_detections || data.all_detections.length}`);\r\n                    console.log(`Total frame detections: ${data.total_frame_detections || data.all_detections.length}`);\r\n                    console.log(`Total frames processed: ${frameBuffer.length}`);\r\n                    return;\r\n                  }\r\n\r\n                  // Handle explicit end signal\r\n                  if (data.end) {\r\n                    console.log('Received end signal, closing stream');\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    return;\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn('Error parsing SSE data:', parseError);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        } catch (streamError) {\r\n          console.error('Stream processing error:', streamError);\r\n          setError('Error processing video stream');\r\n          setIsProcessing(false);\r\n          setLoading(false);\r\n          setIsBuffering(false);\r\n        } finally {\r\n          // Clean up reader\r\n          if (reader) {\r\n            try {\r\n              reader.releaseLock();\r\n            } catch (e) {\r\n              console.warn('Error releasing reader lock:', e);\r\n            }\r\n          }\r\n        }\r\n      };\r\n\r\n      processStream();\r\n\r\n    } catch (error) {\r\n      console.error('Video processing error:', error);\r\n      setError(error.message || 'Video processing failed');\r\n      setLoading(false);\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Stop processing\r\n  const handleStopProcessing = async () => {\r\n    try {\r\n      await axios.post('/api/pavement/stop-video-processing');\r\n      \r\n      setIsProcessing(false);\r\n      setShouldStop(true);\r\n      setIsBuffering(false);\r\n      setIsPlaying(false);\r\n      setLoading(false);\r\n      setError('Video processing stopped');\r\n    } catch (error) {\r\n      console.error('Error stopping processing:', error);\r\n      setError('Failed to stop processing');\r\n    }\r\n  };\r\n\r\n  // Reset all\r\n  const handleReset = () => {\r\n    setVideoFile(null);\r\n    setVideoPreview(null);\r\n    setProcessedVideo(null);\r\n    setVideoResults(null);\r\n    setAllDetections([]);\r\n    setFrameBuffer([]);\r\n    setCurrentFrameIndex(0);\r\n    setIsProcessing(false);\r\n    setShouldStop(false);\r\n    setIsBuffering(false);\r\n    setIsPlaying(false);\r\n    setProcessingProgress(0);\r\n    setError('');\r\n    setSelectedModel('All');\r\n    \r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Playback controls\r\n  const handlePlayPause = () => setIsPlaying(!isPlaying);\r\n  const handleRewind = () => setCurrentFrameIndex(Math.max(currentFrameIndex - 5, 0));\r\n  const handleForward = () => setCurrentFrameIndex(Math.min(currentFrameIndex + 5, frameBuffer.length - 1));\r\n\r\n  // Group detections by type\r\n  const getDetectionSummary = () => {\r\n    const summary = {};\r\n    allDetections.forEach(det => {\r\n      summary[det.type] = (summary[det.type] || 0) + 1;\r\n    });\r\n    return summary;\r\n  };\r\n\r\n  // Get tracking statistics\r\n  const getTrackingStats = () => {\r\n    // Helper function to get unique detections count\r\n    const getUniqueCount = (detections, idField = 'pothole_id') => {\r\n      const uniqueIds = new Set();\r\n      detections.forEach(detection => {\r\n        const id = detection[idField] || detection.track_id;\r\n        if (id !== undefined) {\r\n          uniqueIds.add(id);\r\n        }\r\n      });\r\n      return uniqueIds.size;\r\n    };\r\n\r\n    const totalFrameDetections = allDetections.length;\r\n    const uniquePotholes = getUniqueCount(allDetections.filter(d => d.type === 'Pothole'), 'pothole_id');\r\n    const uniqueCracks = getUniqueCount(allDetections.filter(d => d.type.includes('Crack')), 'track_id');\r\n    const uniqueKerbs = getUniqueCount(allDetections.filter(d => d.type.includes('Kerb')), 'track_id');\r\n    const totalUniqueDetections = uniquePotholes + uniqueCracks + uniqueKerbs;\r\n\r\n    return {\r\n      uniqueDetections: totalUniqueDetections,\r\n      frameDetections: totalFrameDetections,\r\n      duplicatesRemoved: totalFrameDetections - totalUniqueDetections\r\n    };\r\n  };\r\n\r\n  // Format time\r\n  const formatTime = (seconds) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"video-defect-detection\">\r\n      <Row>\r\n        <Col md={6}>\r\n          <Card className=\"mb-4\">\r\n            <Card.Header className=\"bg-primary text-white\">\r\n              <h5 className=\"mb-0\">Video Defect Detection</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              {error && (\r\n                <Alert variant=\"danger\" className=\"mb-3\">\r\n                  {error}\r\n                </Alert>\r\n              )}\r\n\r\n              {/* Model Selection */}\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Detection Model</Form.Label>\r\n                <Form.Select\r\n                  value={selectedModel}\r\n                  onChange={(e) => setSelectedModel(e.target.value)}\r\n                  disabled={isProcessing}\r\n                >\r\n                  {modelOptions.map(option => (\r\n                    <option key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </option>\r\n                  ))}\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n              {/* Input Source Selection */}\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Input Source</Form.Label>\r\n                <Form.Select\r\n                  value={inputSource}\r\n                  onChange={(e) => setInputSource(e.target.value)}\r\n                  disabled={isProcessing}\r\n                >\r\n                  <option value=\"video\">Video Upload</option>\r\n                  <option value=\"camera\">Live Camera Recording</option>\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n              {/* Video Upload */}\r\n              {inputSource === 'video' && (\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Upload Video</Form.Label>\r\n                  <Form.Control\r\n                    type=\"file\"\r\n                    accept=\"video/*\"\r\n                    onChange={handleVideoChange}\r\n                    ref={fileInputRef}\r\n                    disabled={isProcessing}\r\n                  />\r\n                  {videoPreview && (\r\n                    <div className=\"mt-3\">\r\n                      <video\r\n                        src={videoPreview}\r\n                        controls\r\n                        className=\"video-preview\"\r\n                        style={{ maxHeight: '200px' }}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </Form.Group>\r\n              )}\r\n\r\n              {/* Camera Recording */}\r\n              {inputSource === 'camera' && (\r\n                <div className=\"mb-3\">\r\n                  <div className=\"d-flex gap-2 mb-2\">\r\n                    <Button\r\n                      variant={cameraActive ? \"danger\" : \"info\"}\r\n                      onClick={toggleCamera}\r\n                      disabled={isProcessing}\r\n                    >\r\n                      {cameraActive ? 'Stop Camera' : 'Start Camera'}\r\n                    </Button>\r\n                    {isMobile && cameraActive && (\r\n                      <Button\r\n                        variant=\"outline-secondary\"\r\n                        onClick={toggleCameraOrientation}\r\n                        size=\"sm\"\r\n                      >\r\n                        Rotate Camera\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n\r\n                  {cameraActive && (\r\n                    <div className=\"webcam-container\">\r\n                      <Webcam\r\n                        audio={false}\r\n                        ref={webcamRef}\r\n                        screenshotFormat=\"image/jpeg\"\r\n                        width=\"100%\"\r\n                        height=\"auto\"\r\n                        videoConstraints={{\r\n                          width: 640,\r\n                          height: 480,\r\n                          facingMode: cameraOrientation\r\n                        }}\r\n                      />\r\n                      \r\n                      <div className=\"mt-2\">\r\n                        {!isRecording ? (\r\n                          <Button\r\n                            variant=\"success\"\r\n                            onClick={handleStartRecording}\r\n                            disabled={isProcessing}\r\n                          >\r\n                            Start Recording\r\n                          </Button>\r\n                        ) : (\r\n                          <div className=\"d-flex align-items-center gap-2\">\r\n                            <Button\r\n                              variant=\"danger\"\r\n                              onClick={handleStopRecording}\r\n                            >\r\n                              Stop Recording\r\n                            </Button>\r\n                            <span className=\"text-danger\">\r\n                              Recording: {formatTime(recordingTime)} / {formatTime(MAX_RECORDING_TIME)}\r\n                            </span>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {videoPreview && (\r\n                    <div className=\"mt-3\">\r\n                      <video\r\n                        src={videoPreview}\r\n                        controls\r\n                        className=\"video-preview\"\r\n                        style={{ maxHeight: '200px' }}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"action-buttons\">\r\n                <Button\r\n                  variant=\"primary\"\r\n                  onClick={handleProcess}\r\n                  disabled={!isReadyForProcessing() || isProcessing}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <Spinner size=\"sm\" className=\"me-2\" />\r\n                      Processing...\r\n                    </>\r\n                  ) : (\r\n                    'Process Video'\r\n                  )}\r\n                </Button>\r\n                \r\n                {isProcessing && (\r\n                  <Button\r\n                    variant=\"warning\"\r\n                    onClick={handleStopProcessing}\r\n                  >\r\n                    Stop Processing\r\n                  </Button>\r\n                )}\r\n                \r\n                <Button\r\n                  variant=\"secondary\"\r\n                  onClick={handleReset}\r\n                  disabled={isProcessing}\r\n                >\r\n                  Reset\r\n                </Button>\r\n              </div>\r\n\r\n              {/* Processing Progress */}\r\n              {isProcessing && (\r\n                <div className=\"mt-3\">\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <span>Processing Progress:</span>\r\n                    <span>{processingProgress.toFixed(1)}%</span>\r\n                  </div>\r\n                  <div className=\"progress mt-1\">\r\n                    <div\r\n                      className=\"progress-bar\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${processingProgress}%` }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n\r\n        <Col md={6}>\r\n          {/* Processed Video Display */}\r\n          {processedVideo && (\r\n            <Card className=\"mb-4\">\r\n              <Card.Header className=\"bg-success text-white\">\r\n                <h5 className=\"mb-0\">Processed Video</h5>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                <div className=\"processed-video-container\">\r\n                  {isBuffering && (\r\n                    <div className=\"processing-overlay\">\r\n                      <Spinner animation=\"border\" />\r\n                      <span className=\"ms-2\">Buffering video...</span>\r\n                    </div>\r\n                  )}\r\n                  \r\n                  <img\r\n                    src={processedVideo.startsWith('data:') ? processedVideo : `data:image/jpeg;base64,${processedVideo}`}\r\n                    alt=\"Processed frame\"\r\n                    style={{ maxWidth: '100%' }}\r\n                  />\r\n                  \r\n                  {/* Video Controls */}\r\n                  {frameBuffer.length > 0 && (\r\n                    <div className=\"video-controls mt-3\">\r\n                      <div className=\"d-flex gap-2 mb-2\">\r\n                        <Button size=\"sm\" variant=\"outline-primary\" onClick={handleRewind}>\r\n                          ⏪\r\n                        </Button>\r\n                        <Button size=\"sm\" variant=\"outline-primary\" onClick={handlePlayPause}>\r\n                          {isPlaying ? '⏸️' : '▶️'}\r\n                        </Button>\r\n                        <Button size=\"sm\" variant=\"outline-primary\" onClick={handleForward}>\r\n                          ⏩\r\n                        </Button>\r\n                      </div>\r\n                      \r\n                      <Form.Range\r\n                        min={0}\r\n                        max={frameBuffer.length - 1}\r\n                        value={currentFrameIndex}\r\n                        onChange={(e) => setCurrentFrameIndex(Number(e.target.value))}\r\n                      />\r\n                      \r\n                      <div className=\"text-center small text-muted\">\r\n                        Frame {currentFrameIndex + 1} of {frameBuffer.length}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </Card.Body>\r\n            </Card>\r\n          )}\r\n\r\n          {/* Detection Results Table */}\r\n          {allDetections.length > 0 && (\r\n            <Card>\r\n              <Card.Header className=\"bg-info text-white\">\r\n                <h5 className=\"mb-0\">Detection Results</h5>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                {/* Summary */}\r\n                <div className=\"detection-summary mb-3\">\r\n                  <h6>Detection Summary:</h6>\r\n                  <div className=\"mb-2\">\r\n                    {Object.entries(getDetectionSummary()).map(([type, count]) => (\r\n                      <span key={type} className=\"badge bg-secondary me-1\">\r\n                        {type}: {count}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                  \r\n                  {/* Tracking Statistics */}\r\n                  <div className=\"tracking-stats\">\r\n                    <small className=\"text-muted\">\r\n                      <strong>Tracking Stats:</strong> {' '}\r\n                      <span className=\"badge bg-success me-1\">\r\n                        Unique: {getTrackingStats().uniqueDetections}\r\n                      </span>\r\n                      <span className=\"badge bg-info me-1\">\r\n                        Total Frames: {getTrackingStats().frameDetections}\r\n                      </span>\r\n                      <span className=\"badge bg-warning\">\r\n                        Duplicates Removed: {getTrackingStats().duplicatesRemoved}\r\n                      </span>\r\n                    </small>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Separate Tables for Each Defect Type */}\r\n                {(() => {\r\n                  // Helper function to get unique detections by ID with maximum volume\r\n                  const getUniqueDetections = (detections, idField = 'pothole_id') => {\r\n                    const uniqueMap = new Map();\r\n\r\n                    detections.forEach(detection => {\r\n                      const id = detection[idField] || detection.track_id;\r\n                      if (id !== undefined) {\r\n                        const existing = uniqueMap.get(id);\r\n                        if (!existing || (detection.volume_cm3 || detection.volume || 0) > (existing.volume_cm3 || existing.volume || 0)) {\r\n                          uniqueMap.set(id, detection);\r\n                        }\r\n                      }\r\n                    });\r\n\r\n                    return Array.from(uniqueMap.values());\r\n                  };\r\n\r\n                  const allPotholeDetections = allDetections.filter(d => d.type === 'Pothole');\r\n                  const allCrackDetections = allDetections.filter(d => d.type.includes('Crack'));\r\n                  const allKerbDetections = allDetections.filter(d => d.type.includes('Kerb'));\r\n\r\n                  // Get unique detections with maximum volumes\r\n                  const potholeDetections = getUniqueDetections(allPotholeDetections, 'pothole_id');\r\n                  const crackDetections = getUniqueDetections(allCrackDetections, 'track_id');\r\n                  const kerbDetections = getUniqueDetections(allKerbDetections, 'track_id');\r\n\r\n                  return (\r\n                    <div>\r\n                      {/* Pothole Table - Show only if \"All\" or \"Potholes\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Potholes') && (\r\n                        <div className=\"defect-section potholes mb-4\">\r\n                          <h6 className=\"text-danger\">\r\n                            <span className=\"emoji\">🕳️</span>\r\n                            Potholes Detected: {potholeDetections.length}\r\n                          </h6>\r\n                          {potholeDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Area (cm²)</th>\r\n                                    <th>Depth (cm)</th>\r\n                                    <th>Volume (cm³)</th>\r\n                                    <th>Volume Range</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {potholeDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.pothole_id || detection.track_id || index + 1}</td>\r\n                                      <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.max_depth_cm ? detection.max_depth_cm.toFixed(2) : (detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A')}</td>\r\n                                      <td>{(detection.volume_cm3 || detection.volume) ? (detection.volume_cm3 || detection.volume).toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.volume_range || 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No potholes detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Cracks Table - Show only if \"All\" or \"Alligator Cracks\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Alligator Cracks') && (\r\n                        <div className=\"defect-section cracks mb-4\">\r\n                          <h6 className=\"text-success\">\r\n                            <span className=\"emoji\">🪨</span>\r\n                            Cracks Detected: {crackDetections.length}\r\n                          </h6>\r\n                          {crackDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Type</th>\r\n                                    <th>Area (cm²)</th>\r\n                                    <th>Area Range</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {crackDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.track_id || index + 1}</td>\r\n                                      <td>{detection.type}</td>\r\n                                      <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.area_range || 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No cracks detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Kerbs Table - Show only if \"All\" or \"Kerbs\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Kerbs') && (\r\n                        <div className=\"defect-section kerbs mb-4\">\r\n                          <h6 className=\"text-primary\">\r\n                            <span className=\"emoji\">🚧</span>\r\n                            Kerbs Detected: {kerbDetections.length}\r\n                          </h6>\r\n                          {kerbDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Type</th>\r\n                                    <th>Condition</th>\r\n                                    <th>Length</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {kerbDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.track_id || index + 1}</td>\r\n                                      <td>{detection.kerb_type || 'Concrete Kerb'}</td>\r\n                                      <td>{detection.condition || detection.type}</td>\r\n                                      <td>{detection.length_m ? detection.length_m.toFixed(2) : 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No kerbs detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                })()}\r\n              </Card.Body>\r\n            </Card>\r\n          )}\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoDefectDetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACrF,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,eAAe,CAAC;EAC/D,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,aAAa,CAAC;EACzE,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMgE,SAAS,GAAG/D,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgE,YAAY,GAAGhE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMiE,iBAAiB,GAAGjE,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMkE,gBAAgB,GAAGlE,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM;IAAEmE;EAAS,CAAC,GAAGvD,aAAa,CAAC,CAAC;EAEpC,MAAMwD,WAAW,GAAG,EAAE;EACtB,MAAMC,YAAY,GAAG,EAAE;EACvB,MAAMC,kBAAkB,GAAG,EAAE,CAAC,CAAC;;EAE/B;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAoC,CAAC,EAC5D;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAmB,CAAC,EACxD;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,CACnC;;EAED;EACAxE,SAAS,CAAC,MAAM;IACd,IAAIyE,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZ,MAAM;UAAEC,QAAQ;UAAEC;QAAU,CAAC,GAAGF,QAAQ,CAACG,MAAM;QAC/C5C,cAAc,CAAC,GAAG0C,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,KAAKF,SAAS,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;MACnE,CAAC,EACAC,GAAG,IAAK;QACPC,OAAO,CAACtD,KAAK,CAAC,yBAAyB,EAAEqD,GAAG,CAAC;QAC7C9C,cAAc,CAAC,sBAAsB,CAAC;MACxC,CACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnC,SAAS,CAAC,MAAM;IACd,IAAI0C,WAAW,EAAE;MACfsB,iBAAiB,CAACmB,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC5CvC,gBAAgB,CAACwC,IAAI,IAAI;UACvB,IAAIA,IAAI,IAAIhB,kBAAkB,EAAE;YAC9BiB,mBAAmB,CAAC,CAAC;YACrB,OAAOjB,kBAAkB;UAC3B;UACA,OAAOgB,IAAI,GAAG,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAIrB,iBAAiB,CAACmB,OAAO,EAAE;QAC7BI,aAAa,CAACvB,iBAAiB,CAACmB,OAAO,CAAC;MAC1C;IACF;IAEA,OAAO,MAAM;MACX,IAAInB,iBAAiB,CAACmB,OAAO,EAAE;QAC7BI,aAAa,CAACvB,iBAAiB,CAACmB,OAAO,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,CAACzC,WAAW,CAAC,CAAC;;EAEjB;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIwF,gBAAgB;IACpB,IAAIlC,SAAS,IAAIN,WAAW,CAACyC,MAAM,GAAG,CAAC,EAAE;MACvCD,gBAAgB,GAAGJ,WAAW,CAAC,MAAM;QACnCjC,oBAAoB,CAACkC,IAAI,IAAI;UAC3B,IAAIA,IAAI,GAAGrC,WAAW,CAACyC,MAAM,GAAG,CAAC,EAAE;YACjC,OAAOJ,IAAI,GAAG,CAAC;UACjB,CAAC,MAAM;YACL9B,YAAY,CAAC,KAAK,CAAC;YACnB,OAAO8B,IAAI;UACb;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,GAAGjB,YAAY,CAAC;IACzB;IACA,OAAO,MAAM;MACX,IAAIoB,gBAAgB,EAAED,aAAa,CAACC,gBAAgB,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAAClC,SAAS,EAAEN,WAAW,CAAC,CAAC;;EAE5B;EACAhD,SAAS,CAAC,MAAM;IACd,IAAIgD,WAAW,CAACyC,MAAM,GAAG,CAAC,IAAIvC,iBAAiB,GAAGF,WAAW,CAACyC,MAAM,EAAE;MACpEhE,iBAAiB,CAACuB,WAAW,CAACE,iBAAiB,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,CAACA,iBAAiB,EAAEF,WAAW,CAAC,CAAC;;EAEpC;EACA,MAAM0C,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRvE,YAAY,CAACuE,IAAI,CAAC;MAClBrE,eAAe,CAACwE,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC,CAAC;MAC1CnE,iBAAiB,CAAC,IAAI,CAAC;MACvBoC,eAAe,CAAC,IAAI,CAAC;MACrBF,gBAAgB,CAAC,EAAE,CAAC;MACpB9B,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMoE,YAAY,GAAGA,CAAA,KAAM;IACzB1D,eAAe,CAAC,CAACD,YAAY,CAAC;IAC9B,IAAI,CAACA,YAAY,EAAE;MACjBjB,YAAY,CAAC,IAAI,CAAC;MAClBE,eAAe,CAAC,IAAI,CAAC;MACrBE,iBAAiB,CAAC,IAAI,CAAC;MACvBoC,eAAe,CAAC,IAAI,CAAC;MACrBF,gBAAgB,CAAC,EAAE,CAAC;MACpB9B,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMqE,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACpC,SAAS,CAACqB,OAAO,IAAI,CAACrB,SAAS,CAACqB,OAAO,CAACgB,MAAM,EAAE;MACnDtE,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEA,IAAI;MACFkB,iBAAiB,CAAC,EAAE,CAAC;MACrBF,gBAAgB,CAAC,CAAC,CAAC;MACnBF,cAAc,CAAC,IAAI,CAAC;MACpBd,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMuE,aAAa,GAAG,IAAIC,aAAa,CAACvC,SAAS,CAACqB,OAAO,CAACgB,MAAM,EAAE;QAChEG,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFrC,gBAAgB,CAACkB,OAAO,GAAGiB,aAAa;MAExCA,aAAa,CAACG,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvB3D,iBAAiB,CAACsC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEmB,KAAK,CAACC,IAAI,CAAC,CAAC;QAClD;MACF,CAAC;MAEDL,aAAa,CAACO,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC/D,cAAc,EAAE;UAAEgE,IAAI,EAAE;QAAa,CAAC,CAAC;QAC7D,MAAMlB,IAAI,GAAG,IAAImB,IAAI,CAAC,CAACH,IAAI,CAAC,EAAE,kBAAkBI,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAa,CAAC,CAAC;QAC1FzF,YAAY,CAACuE,IAAI,CAAC;QAClBrE,eAAe,CAACwE,GAAG,CAACC,eAAe,CAACY,IAAI,CAAC,CAAC;QAC1CjE,cAAc,CAAC,KAAK,CAAC;QACrBE,gBAAgB,CAAC,CAAC,CAAC;MACrB,CAAC;MAEDuD,aAAa,CAACc,KAAK,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOtF,KAAK,EAAE;MACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAACuF,OAAO,CAAC;MACvDxE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM2C,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIrB,gBAAgB,CAACkB,OAAO,IAAIzC,WAAW,EAAE;MAC3CuB,gBAAgB,CAACkB,OAAO,CAACiC,IAAI,CAAC,CAAC;MAC/BzE,cAAc,CAAC,KAAK,CAAC;MACrBE,gBAAgB,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMwE,uBAAuB,GAAGA,CAAA,KAAM;IACpC5E,oBAAoB,CAAC4C,IAAI,IAAIA,IAAI,KAAK,aAAa,GAAG,MAAM,GAAG,aAAa,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMiC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,OAAQlF,WAAW,KAAK,OAAO,IAAIhB,SAAS,IACpCgB,WAAW,KAAK,QAAQ,IAAIhB,SAAU;EAChD,CAAC;;EAED;EACA,MAAMmG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACD,oBAAoB,CAAC,CAAC,EAAE;MAC3BzF,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,eAAe,CAAC,IAAI,CAAC;IACrBE,aAAa,CAAC,KAAK,CAAC;IACpBoB,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,KAAK,CAAC;IACnBN,cAAc,CAAC,EAAE,CAAC;IAClBE,oBAAoB,CAAC,CAAC,CAAC;IACvBM,qBAAqB,CAAC,CAAC,CAAC;IACxBE,gBAAgB,CAAC,EAAE,CAAC;IACpBlC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBoC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAG;;IAEzB,IAAI;MACF,MAAM2D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEtG,SAAS,CAAC;MACnCoG,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAExG,aAAa,CAAC;MAC/CsG,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAExF,WAAW,CAAC;MAE3CgD,OAAO,CAACyC,GAAG,CAAC,uCAAuC,EAAEzG,aAAa,CAAC;;MAEnE;MACA,MAAM0G,MAAM,GAAG,4BAA4B;;MAE3C;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,MAAM,EAAE;QACnCG,MAAM,EAAE,MAAM;QACdC,IAAI,EAAER;MACR,CAAC,CAAC;MAEF,IAAI,CAACK,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBL,QAAQ,CAACM,MAAM,EAAE,CAAC;MAC3D;;MAEA;MACA,MAAMC,MAAM,GAAGP,QAAQ,CAACG,IAAI,CAACK,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MAEjC,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,OAAO,IAAI,EAAE;YACX,MAAM;cAAEC,IAAI;cAAElE;YAAM,CAAC,GAAG,MAAM6D,MAAM,CAACM,IAAI,CAAC,CAAC;YAC3C,IAAID,IAAI,EAAE;cACRvD,OAAO,CAACyC,GAAG,CAAC,wBAAwB,CAAC;cACrC5F,eAAe,CAAC,KAAK,CAAC;cACtBJ,UAAU,CAAC,KAAK,CAAC;cACjB0B,cAAc,CAAC,KAAK,CAAC;cACrB;YACF;YAEA,MAAMsF,KAAK,GAAGL,OAAO,CAACM,MAAM,CAACrE,KAAK,EAAE;cAAE4B,MAAM,EAAE;YAAK,CAAC,CAAC;YACrD,MAAM0C,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC;YAE/B,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;cACxB,IAAIE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC7B,IAAI;kBACF,MAAMvC,IAAI,GAAGwC,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC;kBAC1CjE,OAAO,CAACyC,GAAG,CAAC,oBAAoB,EAAE;oBAChCyB,QAAQ,EAAE,CAAC,CAAC3C,IAAI,CAAC4C,KAAK;oBACtBC,WAAW,EAAE7C,IAAI,CAAC4C,KAAK,GAAG5C,IAAI,CAAC4C,KAAK,CAAC5D,MAAM,GAAG,CAAC;oBAC/C8D,QAAQ,EAAE9C,IAAI,CAAC8C,QAAQ;oBACvBC,UAAU,EAAE/C,IAAI,CAACgD,WAAW;oBAC5BC,UAAU,EAAEjD,IAAI,CAACiD,UAAU,GAAGjD,IAAI,CAACiD,UAAU,CAACjE,MAAM,GAAG;kBACzD,CAAC,CAAC;kBAEF,IAAIgB,IAAI,CAACkD,OAAO,KAAK,KAAK,EAAE;oBAC1B9H,QAAQ,CAAC4E,IAAI,CAACU,OAAO,IAAI,yBAAyB,CAAC;oBACnDpF,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjB0B,cAAc,CAAC,KAAK,CAAC;oBACrB;kBACF;kBAEA,IAAIoD,IAAI,CAAC4C,KAAK,IAAI,OAAO5C,IAAI,CAAC4C,KAAK,KAAK,QAAQ,IAAI5C,IAAI,CAAC4C,KAAK,CAAC5D,MAAM,GAAG,IAAI,EAAE;oBAC5E;oBACAxC,cAAc,CAACoC,IAAI,IAAI;sBACrB,MAAMuE,SAAS,GAAG,CAAC,GAAGvE,IAAI,EAAEoB,IAAI,CAAC4C,KAAK,CAAC;;sBAEvC;sBACAlG,oBAAoB,CAACyG,SAAS,CAACnE,MAAM,GAAG,CAAC,CAAC;;sBAE1C;sBACA,IAAImE,SAAS,CAACnE,MAAM,KAAK,CAAC,EAAE;wBAC1BpC,cAAc,CAAC,KAAK,CAAC;wBACrBE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;wBACrB9B,iBAAiB,CAACgF,IAAI,CAAC4C,KAAK,CAAC,CAAC,CAAC;sBACjC;sBAEA,OAAOO,SAAS;oBAClB,CAAC,CAAC;;oBAEF;oBACAnI,iBAAiB,CAACgF,IAAI,CAAC4C,KAAK,CAAC;kBAC/B;;kBAEA;kBACA,IAAI5C,IAAI,CAAC8C,QAAQ,KAAKM,SAAS,EAAE;oBAC/BpG,qBAAqB,CAACgD,IAAI,CAAC8C,QAAQ,CAAC;oBACpCrE,OAAO,CAACyC,GAAG,CAAC,wBAAwBlB,IAAI,CAAC8C,QAAQ,CAACvE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;kBAClE;;kBAEA;kBACA,IAAIyB,IAAI,CAACiD,UAAU,IAAIjD,IAAI,CAACiD,UAAU,CAACjE,MAAM,GAAG,CAAC,EAAE;oBACjD9B,gBAAgB,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGoB,IAAI,CAACiD,UAAU,CAAC,CAAC;kBACzD;;kBAEA;kBACA,IAAIjD,IAAI,CAACqD,cAAc,EAAE;oBACvBjG,eAAe,CAAC4C,IAAI,CAAC;oBACrB9C,gBAAgB,CAAC8C,IAAI,CAACqD,cAAc,CAAC;oBACrC/H,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjB0B,cAAc,CAAC,KAAK,CAAC;oBACrBI,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;;oBAE5B;oBACAN,oBAAoB,CAAC,CAAC,CAAC;oBACvBI,YAAY,CAAC,KAAK,CAAC;oBAEnB2B,OAAO,CAACyC,GAAG,CAAC,4BAA4B,CAAC;oBACzCzC,OAAO,CAACyC,GAAG,CAAC,4BAA4BlB,IAAI,CAACsD,uBAAuB,IAAItD,IAAI,CAACqD,cAAc,CAACrE,MAAM,EAAE,CAAC;oBACrGP,OAAO,CAACyC,GAAG,CAAC,2BAA2BlB,IAAI,CAACuD,sBAAsB,IAAIvD,IAAI,CAACqD,cAAc,CAACrE,MAAM,EAAE,CAAC;oBACnGP,OAAO,CAACyC,GAAG,CAAC,2BAA2B3E,WAAW,CAACyC,MAAM,EAAE,CAAC;oBAC5D;kBACF;;kBAEA;kBACA,IAAIgB,IAAI,CAACwD,GAAG,EAAE;oBACZ/E,OAAO,CAACyC,GAAG,CAAC,qCAAqC,CAAC;oBAClD5F,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjB0B,cAAc,CAAC,KAAK,CAAC;oBACrB;kBACF;gBACF,CAAC,CAAC,OAAO6G,UAAU,EAAE;kBACnBhF,OAAO,CAACiF,IAAI,CAAC,yBAAyB,EAAED,UAAU,CAAC;gBACrD;cACF;YACF;UACF;QACF,CAAC,CAAC,OAAOE,WAAW,EAAE;UACpBlF,OAAO,CAACtD,KAAK,CAAC,0BAA0B,EAAEwI,WAAW,CAAC;UACtDvI,QAAQ,CAAC,+BAA+B,CAAC;UACzCE,eAAe,CAAC,KAAK,CAAC;UACtBJ,UAAU,CAAC,KAAK,CAAC;UACjB0B,cAAc,CAAC,KAAK,CAAC;QACvB,CAAC,SAAS;UACR;UACA,IAAI+E,MAAM,EAAE;YACV,IAAI;cACFA,MAAM,CAACiC,WAAW,CAAC,CAAC;YACtB,CAAC,CAAC,OAAO1E,CAAC,EAAE;cACVT,OAAO,CAACiF,IAAI,CAAC,8BAA8B,EAAExE,CAAC,CAAC;YACjD;UACF;QACF;MACF,CAAC;MAED6C,aAAa,CAAC,CAAC;IAEjB,CAAC,CAAC,OAAO5G,KAAK,EAAE;MACdsD,OAAO,CAACtD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACD,KAAK,CAACuF,OAAO,IAAI,yBAAyB,CAAC;MACpDxF,UAAU,CAAC,KAAK,CAAC;MACjBI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMuI,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAM7J,KAAK,CAAC8J,IAAI,CAAC,qCAAqC,CAAC;MAEvDxI,eAAe,CAAC,KAAK,CAAC;MACtBE,aAAa,CAAC,IAAI,CAAC;MACnBoB,cAAc,CAAC,KAAK,CAAC;MACrBE,YAAY,CAAC,KAAK,CAAC;MACnB5B,UAAU,CAAC,KAAK,CAAC;MACjBE,QAAQ,CAAC,0BAA0B,CAAC;IACtC,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdsD,OAAO,CAACtD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,2BAA2B,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAM2I,WAAW,GAAGA,CAAA,KAAM;IACxBnJ,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC,IAAI,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvBoC,eAAe,CAAC,IAAI,CAAC;IACrBF,gBAAgB,CAAC,EAAE,CAAC;IACpBV,cAAc,CAAC,EAAE,CAAC;IAClBE,oBAAoB,CAAC,CAAC,CAAC;IACvBpB,eAAe,CAAC,KAAK,CAAC;IACtBE,aAAa,CAAC,KAAK,CAAC;IACpBoB,cAAc,CAAC,KAAK,CAAC;IACrBE,YAAY,CAAC,KAAK,CAAC;IACnBE,qBAAqB,CAAC,CAAC,CAAC;IACxB5B,QAAQ,CAAC,EAAE,CAAC;IACZV,gBAAgB,CAAC,KAAK,CAAC;IAEvB,IAAI4C,YAAY,CAACoB,OAAO,EAAE;MACxBpB,YAAY,CAACoB,OAAO,CAACZ,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMkG,eAAe,GAAGA,CAAA,KAAMlH,YAAY,CAAC,CAACD,SAAS,CAAC;EACtD,MAAMoH,YAAY,GAAGA,CAAA,KAAMvH,oBAAoB,CAACwH,IAAI,CAACC,GAAG,CAAC1H,iBAAiB,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACnF,MAAM2H,aAAa,GAAGA,CAAA,KAAM1H,oBAAoB,CAACwH,IAAI,CAACG,GAAG,CAAC5H,iBAAiB,GAAG,CAAC,EAAEF,WAAW,CAACyC,MAAM,GAAG,CAAC,CAAC,CAAC;;EAEzG;EACA,MAAMsF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClBtH,aAAa,CAACuH,OAAO,CAACC,GAAG,IAAI;MAC3BF,OAAO,CAACE,GAAG,CAACpE,IAAI,CAAC,GAAG,CAACkE,OAAO,CAACE,GAAG,CAACpE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAClD,CAAC,CAAC;IACF,OAAOkE,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,MAAMC,cAAc,GAAGA,CAAC1B,UAAU,EAAE2B,OAAO,GAAG,YAAY,KAAK;MAC7D,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC3B7B,UAAU,CAACuB,OAAO,CAACO,SAAS,IAAI;QAC9B,MAAMC,EAAE,GAAGD,SAAS,CAACH,OAAO,CAAC,IAAIG,SAAS,CAACE,QAAQ;QACnD,IAAID,EAAE,KAAK5B,SAAS,EAAE;UACpByB,SAAS,CAACK,GAAG,CAACF,EAAE,CAAC;QACnB;MACF,CAAC,CAAC;MACF,OAAOH,SAAS,CAAC5E,IAAI;IACvB,CAAC;IAED,MAAMkF,oBAAoB,GAAGlI,aAAa,CAAC+B,MAAM;IACjD,MAAMoG,cAAc,GAAGT,cAAc,CAAC1H,aAAa,CAACoI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjF,IAAI,KAAK,SAAS,CAAC,EAAE,YAAY,CAAC;IACpG,MAAMkF,YAAY,GAAGZ,cAAc,CAAC1H,aAAa,CAACoI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjF,IAAI,CAACmF,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC;IACpG,MAAMC,WAAW,GAAGd,cAAc,CAAC1H,aAAa,CAACoI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjF,IAAI,CAACmF,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC;IAClG,MAAME,qBAAqB,GAAGN,cAAc,GAAGG,YAAY,GAAGE,WAAW;IAEzE,OAAO;MACLE,gBAAgB,EAAED,qBAAqB;MACvCE,eAAe,EAAET,oBAAoB;MACrCU,iBAAiB,EAAEV,oBAAoB,GAAGO;IAC5C,CAAC;EACH,CAAC;;EAED;EACA,MAAMI,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAG9B,IAAI,CAAC+B,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMG,IAAI,GAAGH,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,oBACEhM,OAAA;IAAKiM,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrClM,OAAA,CAACN,GAAG;MAAAwM,QAAA,gBACFlM,OAAA,CAACL,GAAG;QAACwM,EAAE,EAAE,CAAE;QAAAD,QAAA,eACTlM,OAAA,CAACZ,IAAI;UAAC6M,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACpBlM,OAAA,CAACZ,IAAI,CAACgN,MAAM;YAACH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAC5ClM,OAAA;cAAIiM,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACdxM,OAAA,CAACZ,IAAI,CAACqN,IAAI;YAAAP,QAAA,GACPnL,KAAK,iBACJf,OAAA,CAACT,KAAK;cAACmN,OAAO,EAAC,QAAQ;cAACT,SAAS,EAAC,MAAM;cAAAC,QAAA,EACrCnL;YAAK;cAAAsL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,eAGDxM,OAAA,CAACV,IAAI,CAACqN,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BlM,OAAA,CAACV,IAAI,CAACsN,KAAK;gBAAAV,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxCxM,OAAA,CAACV,IAAI,CAACuN,MAAM;gBACVnJ,KAAK,EAAErD,aAAc;gBACrByM,QAAQ,EAAGhI,CAAC,IAAKxE,gBAAgB,CAACwE,CAAC,CAACE,MAAM,CAACtB,KAAK,CAAE;gBAClDqJ,QAAQ,EAAE9L,YAAa;gBAAAiL,QAAA,EAEtBzI,YAAY,CAACuJ,GAAG,CAACC,MAAM,iBACtBjN,OAAA;kBAA2B0D,KAAK,EAAEuJ,MAAM,CAACvJ,KAAM;kBAAAwI,QAAA,EAC5Ce,MAAM,CAACtJ;gBAAK,GADFsJ,MAAM,CAACvJ,KAAK;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGbxM,OAAA,CAACV,IAAI,CAACqN,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BlM,OAAA,CAACV,IAAI,CAACsN,KAAK;gBAAAV,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCxM,OAAA,CAACV,IAAI,CAACuN,MAAM;gBACVnJ,KAAK,EAAEnC,WAAY;gBACnBuL,QAAQ,EAAGhI,CAAC,IAAKtD,cAAc,CAACsD,CAAC,CAACE,MAAM,CAACtB,KAAK,CAAE;gBAChDqJ,QAAQ,EAAE9L,YAAa;gBAAAiL,QAAA,gBAEvBlM,OAAA;kBAAQ0D,KAAK,EAAC,OAAO;kBAAAwI,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CxM,OAAA;kBAAQ0D,KAAK,EAAC,QAAQ;kBAAAwI,QAAA,EAAC;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAGZjL,WAAW,KAAK,OAAO,iBACtBvB,OAAA,CAACV,IAAI,CAACqN,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BlM,OAAA,CAACV,IAAI,CAACsN,KAAK;gBAAAV,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCxM,OAAA,CAACV,IAAI,CAAC4N,OAAO;gBACXjH,IAAI,EAAC,MAAM;gBACXkH,MAAM,EAAC,SAAS;gBAChBL,QAAQ,EAAEjI,iBAAkB;gBAC5BuI,GAAG,EAAElK,YAAa;gBAClB6J,QAAQ,EAAE9L;cAAa;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,EACD/L,YAAY,iBACXT,OAAA;gBAAKiM,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBlM,OAAA;kBACEqN,GAAG,EAAE5M,YAAa;kBAClB6M,QAAQ;kBACRrB,SAAS,EAAC,eAAe;kBACzBsB,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CACb,EAGAjL,WAAW,KAAK,QAAQ,iBACvBvB,OAAA;cAAKiM,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlM,OAAA;gBAAKiM,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClM,OAAA,CAACX,MAAM;kBACLqN,OAAO,EAAEjL,YAAY,GAAG,QAAQ,GAAG,MAAO;kBAC1CgM,OAAO,EAAErI,YAAa;kBACtB2H,QAAQ,EAAE9L,YAAa;kBAAAiL,QAAA,EAEtBzK,YAAY,GAAG,aAAa,GAAG;gBAAc;kBAAA4K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,EACRnJ,QAAQ,IAAI5B,YAAY,iBACvBzB,OAAA,CAACX,MAAM;kBACLqN,OAAO,EAAC,mBAAmB;kBAC3Be,OAAO,EAAEjH,uBAAwB;kBACjCX,IAAI,EAAC,IAAI;kBAAAqG,QAAA,EACV;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAEL/K,YAAY,iBACXzB,OAAA;gBAAKiM,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlM,OAAA,CAACH,MAAM;kBACL6N,KAAK,EAAE,KAAM;kBACbN,GAAG,EAAEnK,SAAU;kBACf0K,gBAAgB,EAAC,YAAY;kBAC7BC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,MAAM;kBACbC,gBAAgB,EAAE;oBAChBF,KAAK,EAAE,GAAG;oBACVC,MAAM,EAAE,GAAG;oBACXE,UAAU,EAAEpM;kBACd;gBAAE;kBAAA0K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFxM,OAAA;kBAAKiM,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAACrK,WAAW,gBACX7B,OAAA,CAACX,MAAM;oBACLqN,OAAO,EAAC,SAAS;oBACjBe,OAAO,EAAEpI,oBAAqB;oBAC9B0H,QAAQ,EAAE9L,YAAa;oBAAAiL,QAAA,EACxB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAETxM,OAAA;oBAAKiM,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9ClM,OAAA,CAACX,MAAM;sBACLqN,OAAO,EAAC,QAAQ;sBAChBe,OAAO,EAAEhJ,mBAAoB;sBAAAyH,QAAA,EAC9B;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTxM,OAAA;sBAAMiM,SAAS,EAAC,aAAa;sBAAAC,QAAA,GAAC,aACjB,EAACR,UAAU,CAAC3J,aAAa,CAAC,EAAC,KAAG,EAAC2J,UAAU,CAAClI,kBAAkB,CAAC;oBAAA;sBAAA6I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA/L,YAAY,iBACXT,OAAA;gBAAKiM,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBlM,OAAA;kBACEqN,GAAG,EAAE5M,YAAa;kBAClB6M,QAAQ;kBACRrB,SAAS,EAAC,eAAe;kBACzBsB,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAGDxM,OAAA;cAAKiM,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlM,OAAA,CAACX,MAAM;gBACLqN,OAAO,EAAC,SAAS;gBACjBe,OAAO,EAAE/G,aAAc;gBACvBqG,QAAQ,EAAE,CAACtG,oBAAoB,CAAC,CAAC,IAAIxF,YAAa;gBAAAiL,QAAA,EAEjDrL,OAAO,gBACNb,OAAA,CAAAE,SAAA;kBAAAgM,QAAA,gBACElM,OAAA,CAACR,OAAO;oBAACqG,IAAI,EAAC,IAAI;oBAACoG,SAAS,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAExC;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,EAERvL,YAAY,iBACXjB,OAAA,CAACX,MAAM;gBACLqN,OAAO,EAAC,SAAS;gBACjBe,OAAO,EAAEhE,oBAAqB;gBAAAyC,QAAA,EAC/B;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,eAEDxM,OAAA,CAACX,MAAM;gBACLqN,OAAO,EAAC,WAAW;gBACnBe,OAAO,EAAE9D,WAAY;gBACrBoD,QAAQ,EAAE9L,YAAa;gBAAAiL,QAAA,EACxB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGLvL,YAAY,iBACXjB,OAAA;cAAKiM,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlM,OAAA;gBAAKiM,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7ClM,OAAA;kBAAAkM,QAAA,EAAM;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCxM,OAAA;kBAAAkM,QAAA,GAAOvJ,kBAAkB,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNxM,OAAA;gBAAKiM,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BlM,OAAA;kBACEiM,SAAS,EAAC,cAAc;kBACxB+B,IAAI,EAAC,aAAa;kBAClBT,KAAK,EAAE;oBAAEK,KAAK,EAAE,GAAGjL,kBAAkB;kBAAI;gBAAE;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxM,OAAA,CAACL,GAAG;QAACwM,EAAE,EAAE,CAAE;QAAAD,QAAA,GAERvL,cAAc,iBACbX,OAAA,CAACZ,IAAI;UAAC6M,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACpBlM,OAAA,CAACZ,IAAI,CAACgN,MAAM;YAACH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAC5ClM,OAAA;cAAIiM,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACdxM,OAAA,CAACZ,IAAI,CAACqN,IAAI;YAAAP,QAAA,eACRlM,OAAA;cAAKiM,SAAS,EAAC,2BAA2B;cAAAC,QAAA,GACvC3J,WAAW,iBACVvC,OAAA;gBAAKiM,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjClM,OAAA,CAACR,OAAO;kBAACyO,SAAS,EAAC;gBAAQ;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9BxM,OAAA;kBAAMiM,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CACN,eAEDxM,OAAA;gBACEqN,GAAG,EAAE1M,cAAc,CAACwH,UAAU,CAAC,OAAO,CAAC,GAAGxH,cAAc,GAAG,0BAA0BA,cAAc,EAAG;gBACtGuN,GAAG,EAAC,iBAAiB;gBACrBX,KAAK,EAAE;kBAAEY,QAAQ,EAAE;gBAAO;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EAGDrK,WAAW,CAACyC,MAAM,GAAG,CAAC,iBACrB5E,OAAA;gBAAKiM,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClClM,OAAA;kBAAKiM,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClM,OAAA,CAACX,MAAM;oBAACwG,IAAI,EAAC,IAAI;oBAAC6G,OAAO,EAAC,iBAAiB;oBAACe,OAAO,EAAE5D,YAAa;oBAAAqC,QAAA,EAAC;kBAEnE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTxM,OAAA,CAACX,MAAM;oBAACwG,IAAI,EAAC,IAAI;oBAAC6G,OAAO,EAAC,iBAAiB;oBAACe,OAAO,EAAE7D,eAAgB;oBAAAsC,QAAA,EAClEzJ,SAAS,GAAG,IAAI,GAAG;kBAAI;oBAAA4J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACTxM,OAAA,CAACX,MAAM;oBAACwG,IAAI,EAAC,IAAI;oBAAC6G,OAAO,EAAC,iBAAiB;oBAACe,OAAO,EAAEzD,aAAc;oBAAAkC,QAAA,EAAC;kBAEpE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENxM,OAAA,CAACV,IAAI,CAAC8O,KAAK;kBACTnE,GAAG,EAAE,CAAE;kBACPF,GAAG,EAAE5H,WAAW,CAACyC,MAAM,GAAG,CAAE;kBAC5BlB,KAAK,EAAErB,iBAAkB;kBACzByK,QAAQ,EAAGhI,CAAC,IAAKxC,oBAAoB,CAAC+L,MAAM,CAACvJ,CAAC,CAACE,MAAM,CAACtB,KAAK,CAAC;gBAAE;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC,eAEFxM,OAAA;kBAAKiM,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,GAAC,QACtC,EAAC7J,iBAAiB,GAAG,CAAC,EAAC,MAAI,EAACF,WAAW,CAACyC,MAAM;gBAAA;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP,EAGA3J,aAAa,CAAC+B,MAAM,GAAG,CAAC,iBACvB5E,OAAA,CAACZ,IAAI;UAAA8M,QAAA,gBACHlM,OAAA,CAACZ,IAAI,CAACgN,MAAM;YAACH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACzClM,OAAA;cAAIiM,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACdxM,OAAA,CAACZ,IAAI,CAACqN,IAAI;YAAAP,QAAA,gBAERlM,OAAA;cAAKiM,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClM,OAAA;gBAAAkM,QAAA,EAAI;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BxM,OAAA;gBAAKiM,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAClBoC,MAAM,CAACC,OAAO,CAACrE,mBAAmB,CAAC,CAAC,CAAC,CAAC8C,GAAG,CAAC,CAAC,CAAC/G,IAAI,EAAEuI,KAAK,CAAC,kBACvDxO,OAAA;kBAAiBiM,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GACjDjG,IAAI,EAAC,IAAE,EAACuI,KAAK;gBAAA,GADLvI,IAAI;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNxM,OAAA;gBAAKiM,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7BlM,OAAA;kBAAOiM,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAC3BlM,OAAA;oBAAAkM,QAAA,EAAQ;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,GAAG,eACrCxM,OAAA;oBAAMiM,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,UAC9B,EAAC5B,gBAAgB,CAAC,CAAC,CAACiB,gBAAgB;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACPxM,OAAA;oBAAMiM,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,GAAC,gBACrB,EAAC5B,gBAAgB,CAAC,CAAC,CAACkB,eAAe;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACPxM,OAAA;oBAAMiM,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,sBACb,EAAC5B,gBAAgB,CAAC,CAAC,CAACmB,iBAAiB;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL,CAAC,MAAM;cACN;cACA,MAAMiC,mBAAmB,GAAGA,CAAC5F,UAAU,EAAE2B,OAAO,GAAG,YAAY,KAAK;gBAClE,MAAMkE,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;gBAE3B9F,UAAU,CAACuB,OAAO,CAACO,SAAS,IAAI;kBAC9B,MAAMC,EAAE,GAAGD,SAAS,CAACH,OAAO,CAAC,IAAIG,SAAS,CAACE,QAAQ;kBACnD,IAAID,EAAE,KAAK5B,SAAS,EAAE;oBACpB,MAAM4F,QAAQ,GAAGF,SAAS,CAACG,GAAG,CAACjE,EAAE,CAAC;oBAClC,IAAI,CAACgE,QAAQ,IAAI,CAACjE,SAAS,CAACmE,UAAU,IAAInE,SAAS,CAACoE,MAAM,IAAI,CAAC,KAAKH,QAAQ,CAACE,UAAU,IAAIF,QAAQ,CAACG,MAAM,IAAI,CAAC,CAAC,EAAE;sBAChHL,SAAS,CAACM,GAAG,CAACpE,EAAE,EAAED,SAAS,CAAC;oBAC9B;kBACF;gBACF,CAAC,CAAC;gBAEF,OAAOsE,KAAK,CAACC,IAAI,CAACR,SAAS,CAACS,MAAM,CAAC,CAAC,CAAC;cACvC,CAAC;cAED,MAAMC,oBAAoB,GAAGvM,aAAa,CAACoI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjF,IAAI,KAAK,SAAS,CAAC;cAC5E,MAAMoJ,kBAAkB,GAAGxM,aAAa,CAACoI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjF,IAAI,CAACmF,QAAQ,CAAC,OAAO,CAAC,CAAC;cAC9E,MAAMkE,iBAAiB,GAAGzM,aAAa,CAACoI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjF,IAAI,CAACmF,QAAQ,CAAC,MAAM,CAAC,CAAC;;cAE5E;cACA,MAAMmE,iBAAiB,GAAGd,mBAAmB,CAACW,oBAAoB,EAAE,YAAY,CAAC;cACjF,MAAMI,eAAe,GAAGf,mBAAmB,CAACY,kBAAkB,EAAE,UAAU,CAAC;cAC3E,MAAMI,cAAc,GAAGhB,mBAAmB,CAACa,iBAAiB,EAAE,UAAU,CAAC;cAEzE,oBACEtP,OAAA;gBAAAkM,QAAA,GAEG,CAAC7L,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,UAAU,kBACvDL,OAAA;kBAAKiM,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3ClM,OAAA;oBAAIiM,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACzBlM,OAAA;sBAAMiM,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,uBACf,EAAC+C,iBAAiB,CAAC3K,MAAM;kBAAA;oBAAAyH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,EACJ+C,iBAAiB,CAAC3K,MAAM,GAAG,CAAC,gBAC3B5E,OAAA;oBAAKiM,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxClM,OAAA,CAACP,KAAK;sBAACiQ,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAAC/J,IAAI,EAAC,IAAI;sBAAAqG,QAAA,gBACrClM,OAAA;wBAAAkM,QAAA,eACElM,OAAA;0BAAAkM,QAAA,gBACElM,OAAA;4BAAAkM,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACrBxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRxM,OAAA;wBAAAkM,QAAA,EACGqD,iBAAiB,CAACvC,GAAG,CAAC,CAACrC,SAAS,EAAEkF,KAAK,kBACtC7P,OAAA;0BAAAkM,QAAA,gBACElM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACmF,UAAU,IAAInF,SAAS,CAACE,QAAQ,IAAIgF,KAAK,GAAG;0BAAC;4BAAAxD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAClExM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACoF,QAAQ,GAAGpF,SAAS,CAACoF,QAAQ,CAAC5L,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrExM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACqF,YAAY,GAAGrF,SAAS,CAACqF,YAAY,CAAC7L,OAAO,CAAC,CAAC,CAAC,GAAIwG,SAAS,CAACsF,QAAQ,GAAGtF,SAAS,CAACsF,QAAQ,CAAC9L,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAM;4BAAAkI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpIxM,OAAA;4BAAAkM,QAAA,EAAMvB,SAAS,CAACmE,UAAU,IAAInE,SAAS,CAACoE,MAAM,GAAI,CAACpE,SAAS,CAACmE,UAAU,IAAInE,SAAS,CAACoE,MAAM,EAAE5K,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrHxM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACuF,YAAY,IAAI;0BAAK;4BAAA7D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GALnCqD,KAAK;0BAAAxD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAMV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENxM,OAAA;oBAAKiM,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC9D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EAGA,CAACnM,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,kBAAkB,kBAC/DL,OAAA;kBAAKiM,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzClM,OAAA;oBAAIiM,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC1BlM,OAAA;sBAAMiM,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,qBAChB,EAACgD,eAAe,CAAC5K,MAAM;kBAAA;oBAAAyH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,EACJgD,eAAe,CAAC5K,MAAM,GAAG,CAAC,gBACzB5E,OAAA;oBAAKiM,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxClM,OAAA,CAACP,KAAK;sBAACiQ,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAAC/J,IAAI,EAAC,IAAI;sBAAAqG,QAAA,gBACrClM,OAAA;wBAAAkM,QAAA,eACElM,OAAA;0BAAAkM,QAAA,gBACElM,OAAA;4BAAAkM,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAI;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACbxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRxM,OAAA;wBAAAkM,QAAA,EACGsD,eAAe,CAACxC,GAAG,CAAC,CAACrC,SAAS,EAAEkF,KAAK,kBACpC7P,OAAA;0BAAAkM,QAAA,gBACElM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACE,QAAQ,IAAIgF,KAAK,GAAG;0BAAC;4BAAAxD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1CxM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAAC1E;0BAAI;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACzBxM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACoF,QAAQ,GAAGpF,SAAS,CAACoF,QAAQ,CAAC5L,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrExM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACwF,UAAU,IAAI;0BAAK;4BAAA9D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAJjCqD,KAAK;0BAAAxD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAKV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENxM,OAAA;oBAAKiM,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC5D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EAGA,CAACnM,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,OAAO,kBACpDL,OAAA;kBAAKiM,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxClM,OAAA;oBAAIiM,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC1BlM,OAAA;sBAAMiM,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,oBACjB,EAACiD,cAAc,CAAC7K,MAAM;kBAAA;oBAAAyH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACJiD,cAAc,CAAC7K,MAAM,GAAG,CAAC,gBACxB5E,OAAA;oBAAKiM,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxClM,OAAA,CAACP,KAAK;sBAACiQ,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAAC/J,IAAI,EAAC,IAAI;sBAAAqG,QAAA,gBACrClM,OAAA;wBAAAkM,QAAA,eACElM,OAAA;0BAAAkM,QAAA,gBACElM,OAAA;4BAAAkM,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAI;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACbxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAS;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAClBxM,OAAA;4BAAAkM,QAAA,EAAI;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRxM,OAAA;wBAAAkM,QAAA,EACGuD,cAAc,CAACzC,GAAG,CAAC,CAACrC,SAAS,EAAEkF,KAAK,kBACnC7P,OAAA;0BAAAkM,QAAA,gBACElM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACE,QAAQ,IAAIgF,KAAK,GAAG;0BAAC;4BAAAxD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1CxM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAACyF,SAAS,IAAI;0BAAe;4BAAA/D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACjDxM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAAC0F,SAAS,IAAI1F,SAAS,CAAC1E;0BAAI;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAChDxM,OAAA;4BAAAkM,QAAA,EAAKvB,SAAS,CAAC2F,QAAQ,GAAG3F,SAAS,CAAC2F,QAAQ,CAACnM,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAJ9DqD,KAAK;0BAAAxD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAKV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENxM,OAAA;oBAAKiM,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC3D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAEV,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpM,EAAA,CAl4BID,oBAAoB;EAAA,QA8BHL,aAAa;AAAA;AAAAyQ,EAAA,GA9B9BpQ,oBAAoB;AAo4B1B,eAAeA,oBAAoB;AAAC,IAAAoQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}